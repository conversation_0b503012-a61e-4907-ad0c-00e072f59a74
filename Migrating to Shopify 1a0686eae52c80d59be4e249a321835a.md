# Migrating to Shopify

1. After first upload, check product bundle names, to be renamed inside CRM before reimport
2. Create bundles to be uploaded to Mintsoft SKU
3. Clean up subscriptions - ‣ 
4. Clean up orders - ‣ 

Manually map products to the ones already on the site - so it doesn’t create new copy

[Project Kickoff](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Project%20Kickoff%201ad686eae52c80939e2ad18f01b9d76d.md)

[Migrating of Historical Data](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md)

[Migrating Customers Payment Tokens](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Migrating%20Customers%20Payment%20Tokens%201af686eae52c80969cbad9ead4885cf6.md)

[Subscriptions Migration](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md)

[“CRM (Syluis) → Shopify” Live Sync](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/%E2%80%9CCRM%20(Syluis)%20%E2%86%92%20Shopify%E2%80%9D%20Live%20Sync%201d5686eae52c80b7bd3ef39f454e9f01.md)

[Key Statistics](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Key%20Statistics%201a3686eae52c80c2bd29d28356ade5be.md)

[Changelog](Migrating%20to%20Shopify%201a0686eae52c80d59be4e249a321835a/Changelog%201ed686eae52c80f2b77de4865a7aaa24.md)