# Subscriptions Migration

<aside>
💡

**Warning**! This migration **must** be performed **after** the 

1. [Migrating of Historical Data](Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) 
2. [Migrating Customers Payment Tokens](Migrating%20Customers%20Payment%20Tokens%201af686eae52c80969cbad9ead4885cf6.md) 

exports are finished.

</aside>

# About

In **Sylius CRM** we have **Stripe**, **PayPal**, **Klarna** subscriptions.

- SQL queries to get how many of each type (expand to see)
    
    ```sql
    # Stripe Subscriptions
    SELECT
    	count(*)
    FROM
    	sylius_order o
    WHERE
    	is_subscription = 1
    	AND is_subscription_canceled = 0
    	AND state = "fulfilled"
    	AND EXISTS (
    		SELECT
    			1
    		FROM
    			sylius_payment p
    		WHERE
    			p.order_id = o.id
    			AND p.method_id = 1
    	);
    	
    # PayPal subscriptions
    SELECT
    	count(*)
    FROM
    	sylius_order o
    WHERE
    	is_subscription = 1
    	AND is_subscription_canceled = 0
    	AND state = "fulfilled"
    	AND EXISTS (
    		SELECT
    			1
    		FROM
    			sylius_payment p
    		WHERE
    			p.order_id = o.id
    			AND p.method_id = 2
    	);
    
    # Klarna Subscriptions
    SELECT
    	count(*)
    FROM
    	sylius_order o
    WHERE
    	is_subscription = 1
    	AND is_subscription_canceled = 0
    	AND state = "fulfilled"
    	AND EXISTS (
    		SELECT
    			1
    		FROM
    			sylius_payment p
    		WHERE
    			p.order_id = o.id
    			AND p.method_id = 3
    	);
    ```
    

**We migrate only** **Stripe** subscriptions due to tokens incompatibility for PayPal, and the absent direct way (and a small amount) of Klarna subscriptions.

Both **PayPal** subscriptions and **Klarna** subscriptions decided to migrate by asking customers to re-create their subscriptions on the Shopify platform after sites migrations.

On **Shopify side** we use the [**Appstle Subscriptions app**](https://apps.shopify.com/subscriptions-by-appstle) that is a wrapper for the Shopify’s Subscriptions functionality that adds a fluent configuration of a customer portal, emails, a lot of additional features above the plain Subscriptions functionality in Shopify.

# Documentation and Links:

- **Appstle Subscriptions**
    - https://intercom.help/appstle/en/collections/2776373-subscriptions
    - https://intercom.help/appstle/en/articles/8993352-integrate-appstle-into-a-headless-store
    - https://subscription-admin.appstle.com/swagger-ui/appstle-subscriptions-api.html#/subscription-contract-details-resource/updateOrderNoteV2 (Appstle Subscriptions. API)
    - https://admin.shopify.com/store/drsisterskincare-dev/apps/subscription-by-rhem/dashboard/settings/migrations Appstle app’s built-in documentation on Subscriptions migration
    - https://apps.shopify.com/subscriptions-by-appstle
    - https://appstle.com/products/ecommerce-subscriptions/
    - https://intercom.help/appstle/en/articles/5434601-migration-process-v1
    - https://www.reddit.com/r/shopify/comments/17sqhpg/switch_to_another_subscription_app/
    - Importing Subscriptions via CSV (might be useful as contains almost the same data, as the corresponding Appstle API endpoint):
        - Video explanation: https://www.loom.com/share/93f5bdfb53044fbfa579ec24b26c073c?sid=39420f8e-22e3-4e27-9d1a-39010228b7dd
        - [Appstle Subscription Data Sample Template for Migration](https://docs.google.com/spreadsheets/d/1g-03DpvjtBEN3KKTY_79BYJOY2zI7quoQYJWGdFSTBk/edit?gid=0#gid=0)
- **Shopify subscriptions**
    - https://shopify.dev/docs/apps/build/purchase-options/subscriptions Subscriptions
    - https://shopify.dev/docs/apps/build/purchase-options/subscriptions/migrate-to-subscriptions-api/migrate-subscription-contracts#step-2-create-billing-attempts Migrate Existing Subscriptions
    - https://help.shopify.com/en/manual/products/purchase-options/subscriptions
    - https://www.sealsubscriptions.com/article/shopify-subscriptions-to-seal
    - https://www.reddit.com/r/shopify/comments/17sqhpg/switch_to_another_subscription_app/
    - https://community.shopify.com/c/subscriptions-apis/use-existing-payment-methods-when-migrating-subscription/td-p/1243077
    - https://shopify.dev/docs/api/usage/access-scopes#subscription-apis-permissions
    - https://support.simplee.best/en/articles/6032104-connecting-stripe-in-preparation-for-a-migration
    - https://learn.loopwork.co/en/article/payments-migration-gy6u7e/
    - https://loopsubscriptions.crunch.help/en/migrate-customers-and-subscriptions/migrate-customers-payment-methods-from-stripe-to-shopify
    - https://help.ordergroove.com/hc/en-us/articles/1500012883701-Setting-up-to-Migrate-to-Shopify-Payments#q-is-stripe-used-as-a-secondary-gateway-through-the-migration-only-or-for-the-life-of-the-customer-s-subscription--0
    - https://docs.paywhirl.com/en/articles/5026957-how-to-connect-stripe-to-shopify-to-migrate-existing-subscriptions
    - https://community.shopify.com/c/payments-shipping-and/klarna-through-shopify-payments-for-existing-klarna-users/m-p/2889507/highlight/true Some Klarna Payments Configuration on Shopify
    - https://help.shopify.com/en/manual/payments/shopify-payments/local-payment-methods/klarna/klarna-shopify-payments

# Preparation for Migration

1. **Obtain the API token**
    
    We use Appstle API - so, we need to obtain their API access/key via direct communication (it’s paid, Karolis did it directly).
    
2. **Disable customers notifications** in Appstle Subscriptions
    1. Subscription Created
    2. Upcoming Order
    3. Product Price Updated
    4. Next Order Date Updated
    5. Subscription Product Added
    6. Subscription Product Removed
    7. Subscription Product Replaced
3. **Configure Shopify Sync on the Sylius side**: enable Shopify DB logging, set Appstle’s API key:
    - Go to the ***/admin/shopify_sync/config*** URL in Sylius Admin UI (the “Sync Configuration” page of Shopify)
    - Turn on the “Enable database export logging” checkbox
    - Put the **Appstle Subscriptions API key** to the corresponding input
    - Put the **Shopify shop Admin UI URL ID** (e.g. the part "q79sax-ag" in [https://admin.shopify.com/store/q79sax-ag/](https://admin.shopify.com/store/q79sax-ag/)) to the corresponding input
    - (Ignore the “Shopify shop domain” input - it’s not used here)
    - Press the “Save changes” button
- **Migrate all customers and orders to Shopify**
    - Disable mintsoft integration
- **Disable Sylius cron renewing subscriptions** while migrations happen
- Enable back email notifications

# How It Works (the App’s Exporting Flow)

## We have the following requirements for subscriptions filtering:

1. The subscriptions should be NOT cancelled
2. Subscription might be paused, but in this case - we have check if it has a corresponding scheduled/upcoming “resume_subscription” type task in the “sylius_order_tasks” DB table:
    1. If “yes” - we export subscription with the “next order date” equals the date set in the “executeAtTime” filed of the found scheduled “resume_subscription” order task.
    2. If “no” - we do not export the subscription (we imply that it’s essentially been cancelled)
3. We export only subscriptions of customers that have a “Stripe Customer ID” in sylius_customer DB (here we imply that they have “Stripe” subscriptions).

## The App’s flow is the following:

- 1. We retrieve all subscriptions from the database using the following SQL query (pagination is omitted here for clarity):
    
    ```sql
    SELECT
    	o.*
    FROM
    	sylius_order o
    	INNER JOIN sylius_order_item oi ON o.id = oi.order_id
    WHERE
    	oi.quantity > 0
    	AND o.state = 'fulfilled'
    	AND o.is_subscription = 1
    	AND o.is_subscription_canceled = 0
    	AND o.channel_id = 2
    	AND o.shopify_subscription_export_error IS NULL
    	AND o.shopify_subscription_export_status IS NULL;
    ```
    
- 2. We validate each subscription order to determine its eligibility for export to Shopify:
    1. Is it a Stripe subscription
    2. If the Subscription is paused - does it have the corresponding “resume_subscription” task record in the “sylius_order_tasks” DB table
    
    If the subscription does not pass these validation checks, we set its *shopify_subscription_export_status* to NOT_ELIGIBLE and provide details on why it is not eligible in the *shopify_subscription_export_error* field of the Order entity. The export process ends at this point for ineligible subscriptions.
    
- 3. We create and send a ‘Create Subscription Contract’ request to the Appstle Subscriptions API
    
    A Swagger UI with examples of the request and response: https://subscription-admin.appstle.com/swagger-ui/appstle-subscriptions-api.html#/subscription-contract-details-resource/updateOrderNoteV2.
    
    **Notable mapping**:
    
    - We DO NOT specify the customer’s payment method (leave it null) and assume that the exported subscription contract will use the customer’s default payment method, which was previously exported using the [Migrating Customers Payment Tokens](Migrating%20Customers%20Payment%20Tokens%201af686eae52c80969cbad9ead4885cf6.md) functionality.
        - Sometimes we’re getting an error “Payment Method not found” in Shopify - despite we have exported it for the customer. It means that the customer’s stripe data is not valid anymore and should be taken from the last successfully paid customer’s orders, via the command
            
            ```sql
            bin/console malaberg_sylius_shopify_sync_plugin:fix_stripe_customer_ids --order_ids=XXXXX1,XXXXX2,XXXXX7 --force -vvv
            ```
            
            <aside>
            💡
            
            Run the command above without the --force option to just check which changes would be applied.
            
            </aside>
            
    - We always set the subscription contract status to ACTIVE.
    - We set the same values for the Billing Interval and Payment Interval. While it is possible to make them different (e.g., sending three monthly shipments but charging the customer only once every three months), we always set DAY here, and the Billing Interval Count and Payment Interval Count are both set to the Order’s *calculatedSubscriptionFrequency*, which is always in *days* in Sylius.
    - Shipping Cost: If the order has a positive value in its summarized adjustments at the ORDER level (not at the order ITEM or order item UNIT level - we do not take those into account here; see the “Discounts” section below for details), we map this value as Delivery Price Amount
    - Discounts: We always map any discounts as a reduction in the subscription item prices as follows:
        - If the subscription has a negative value (a discount) in its ORDER-level adjustments, we distribute this discount among ALL items in the original order (including both subscription items and one-time purchases). We then reduce the unit price of each subscription item accordingly.
        - If the subscription has a negative value in any Subscription Item at the ITEM and/or item UNIT levels, we sum these discounts, divide the total by the item’s quantity, and reduce the item’s unit price by this calculated value (i.e., the per-unit discount).
- 4. If we receive an error in Appstle’s API response, we
    1. Save it to the shopify_subscription_export_error field for the Order
    2. Set the shopify_subscription_export_status to “ERROR”
- 5. Once we receive a successful response, we
    1. Put the “EXPORTED” value the shopify_subscription_export_status” field of the Order
    2. Put a new record to the “shopify_exported_subscription” DB table with most of data we got in the Appstle’s API response
    3. Add an “Order Action History” record (that is shown on the Order’s page in Sylius Admin UI) with a note that the subscription has been exported, and a link to it in Shopify Admin UI.
    4. If it was a “paused” subscription with scheduled “resume_subscription” tasks, we also
        1. Cancel all scheduled “resume_subscription” and “notify_admin_resume” tasks for the order
        2. Add a record with a note about the cancelled tasks to the “Order Action History”
    5. We make an additional call to Appstle’s API endpoint [update-custom-note-attributes](https://subscription-admin.appstle.com/swagger-ui/appstle-subscriptions-api.html#/subscription-contract-details-resource/updateOrderNoteAttributes) to send an information about the original subscription order as **custom attributes** of the just created subscription contract:
        1. original_order_shopify_id
        2. original_order_crm_id
        3. original_order_crm_number
        
        Once the above is performed - the order→shopify_subscription_export_status obtains the value ORIGINAL_ORDER_INFO_ADDED.
        

## Detailed Exporting Log

If the “Enable database export logging” option is enabled (in Sylius Admin UI → Shopify → Sync Configuration), the app also logs detailed information about each step performed (including the generated request, received response, etc.) as records in the shopify_export_log database table.

# How to Perform Migration

## 1. Testing “per-order” export run

First, we need to perform a test export for one subscription/order by manually running the following command in the CLI:

```bash
bin/console malaberg_sylius_shopify_sync_plugin:appstle_export_subscription_contracts {CHANNEL_CODE} --order_id=XXX -vvv
```

## 2. Testing manual batch export run

Next, we need to test how bulk exporting works by manually running the command once or multiple times (for example, exporting three customers in one batch):

```bash
bin/console malaberg_sylius_shopify_sync_plugin:appstle_export_subscription_contracts {CHANNEL_CODE} -b 3 -vvv
```

## 3. Full export

Add the following command to cron (it is a locking command, so it is safe to run every minute):

```bash
* * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:appstle_export_subscription_contracts {CHANNEL_CODE} -b 100
```

## 4. Run an Export of Original Order Information to the Created Subscriptions Contracts

In the current/actual version of the app, the original orders information is being automatically sent/set to the just created subscription contract at the final step (via additional call to Appsle’s API). But if there are subscriptions which have been created until the mentioned functionality has been deployed - there is a command that can/will add the original order information to exported subscriptions:

### Run for a particular order

```bash
bin/console malaberg_sylius_shopify_sync_plugin:appstle_add_original_order_info_to_subscription_contract --order_id=XXXXX -vvv
```

### Full export (put to the cron)

```bash
* * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:appstle_add_original_order_info_to_subscription_contract -b 100
```

### As a result:

- If the call was **successful**:
    - The order’s row in the sylius_order table will obtain shopify_subscription_export_status = ‘ORIGINAL_ORDER_INFO_ADDED’
    - The corresponding row in the shopisy_exported_subscription will obtain the “shopify_id” in the origin_order_shopify_id field
- If the call had any **issue**
    - sylius_order.shopify_subscription_export_status will become “ORIGINAL_ORDER_INFO_ERROR”
    - sylius_order.shopify_subscription_export_error field will contain the detailed error message

# How to Verify the Migration Correctness

## Preliminary Checks of Subscriptions Exporting Results in Sylius/CRM DB

### How Many Subscriptions Are Eligible for Processing

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	is_subscription = 1
	AND is_subscription_canceled = 0
	AND state = "fulfilled";
```

### How Many Subscriptions Have Been Processed During the Exporting

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	shopify_subscription_export_status IS NOT NULL;
```

### How Many Subscriptions Have Been Successfully Exported

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	shopify_subscription_export_status IN ('EXPORTED', 'ORIGINAL_ORDER_INFO_ADDED');
```

### How Many Subscriptions Have Been Filtered Out as Not Eligible for Exporting

(e.g. it’s PayPal or Klarna subscriptions, or it’s a “paused” subscription but without the “renew” date)

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	shopify_subscription_export_status = 'NOT_ELIGIBLE';
```

### How Many Exports are Broken due to Errors

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	shopify_subscription_export_status = 'ERROR';
```

### A Discrepancy Between Eligible for Processing and Processed Subscriptions

If there are “eligible for processing”, but not processed subscriptions - then there is some issue in the exporting process:

```sql
SELECT
	count(*)
FROM
	sylius_order o
WHERE
	is_subscription = 1
	AND is_subscription_canceled = 0
	AND state = "fulfilled"
	AND shopify_subscription_export_status IS NULL;
```

### (Minor check) A Discrepancy Between Sent Subscription Data and Received Subscriptions Data Back in API Response

It’s a **minor**, almost impossible case - when the amount of the exported subscriptions does not equal to the amount of the subscriptions data records saved to the shopify_exported_subscription DB table - it’s because we save the data to this table simultaneously with marking the exported subscription as “EXPORTED”, actually.

But if there is any discrepancy - it means the app’s exporting flow had some unexpected issues, which should be investigated:

```sql
SELECT
	count(*) as exported_subscriptions_amount
FROM
	sylius_order o
WHERE
	shopify_subscription_export_status IN ('EXPORTED', 'ORIGINAL_ORDER_INFO_ADDED');

SELECT
	count(*) as saved_subscriptions_records_amount
FROM
	shopify_exported_subscription;
```

If the **saved_subscriptions_records_amount** from the query above is **bigger** than the **exported_subscriptions_amount** - it means we have exported more subscriptions, that we have in the DB - maybe we have several successful tries - please, make sure that you have cancelled the redundantly exported subscriptions on Shopify/Appstle side.

## Checks On the Resulting Data Imported Back from Shopify

## Export Back the Shopify’s Subscription Data

1. Go to Shopify Store Admin UI → Apps → Appstle Subscription → Subscriptions
2. Export all subscriptions to CSV file (for the moment only Karolis has enough permissions on prod sites for this operation)
3. Import the resulting CSV file into a new table in the Sylius/CMR DB (all fields as text files - will be enough)

## Prepare the Shopify/Appstle’s Data for Comparison

Perform the following SQL queries on the table with the imported Shopify/Appstle subscriptions data:

```sql
UPDATE your_new_table
SET ID = CONCAT('gid://shopify/SubscriptionContract/', ID);

ALTER TABLE your_new_table
ADD INDEX idx_ex_back_shopify_subs_id (ID(100));
```

## Checks on the Exported subscriptions data from Appstle subscriptions app

- How many subscriptions have been exported from Sylius/CRM
    
    ```sql
    SELECT
    	count(*) amount_of_subscrioptions_exported_to_appstle
    FROM
    	your_table_name
    WHERE
    	`Line selling plan ID` = '';
    ```
    
    The **amount_of_subscrioptions_exported_to_appstle** number should be equal to [the number of the exported subscriptions in Sylius/CRM db](Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md).
    
- The resulting number of enabled (active) subscriptions
    
    ```sql
    SELECT
    	count(*)
    FROM
    	your_table_name
    WHERE
    	`Line selling plan ID` = ''
    	AND `Status` = 'active';
    ```
    

## Troubleshooting

Get Detailed Errors and Exporting Information for a Subscription Order

In case of any error is contained in the sylius_order.shopify_subscription_export_error field, we can get detailed information from the following sources:

1. Get the Request and Response in the corresponding shopify_exported_subscription table:
    
    ```sql
    SELECT * FROM shopify_exported_subscription WHERE order_id = XXXXX;
    ```
    
2. Get detailed information about the performed steps during the exporting flow from the shopify_export_log table:
    
    ```sql
    SELECT
    	*
    FROM
    	shopify_export_log
    WHERE
    	entity_id = XXXXX
    	AND operation = 'export_subscription_contract'
    ORDER BY
    	id ASC;
    ```
    

### Fix of Wrong Delivery/Shipping Address Issues

It’s the most often exporting error, when Appstle/Shopify complains that

- `Delivery method shipping address province code can't be blank`
- `Shipping address country Country/region not supported`

or related.

They should be handled by fixing issues with the shipping address of the subscription order manually in the DB, and then re-exporting the subscription (see below for how to re-run it).

### Ultimate Fix

If the exported subscription data is wrong and/but cannot be fixed by changing order’s data in the DB (e.g. if it would make it non-compatible with the order exporting) - the ultimate way to fix the subscription exporting is to put an appropriately formed subscription creation JSON request data into the order→shopify_subscription_export_adjusted_request feld. If it’s not empty - it will be used in the subscription creation process.

An example of the request can be taken from the shopify_exported_subscription DB table, the request field. For example it may contain something like:

```sql
{
    "customerId": "gid:\/\/shopify\/Customer\/8769027655544",
    "status": "ACTIVE",
    "nextBillingDate": "2025-03-15T00:18:28.646Z",
    "billingIntervalType": "DAY",
    "billingIntervalCount": 30,
    "deliveryIntervalType": "DAY",
    "deliveryIntervalCount": 30,
    "deliveryFirstName": "Maria",
    "deliveryLastName": "Swift",
    "deliveryAddress1": "23 Park Way",
    "deliveryCity": "York",
    "deliveryZip": "XXXXX",
    "deliveryCountryCode": "GB",
    "deliveryPhone": "+44733334555",
    "currencyCode": "GBP",
    "lines": [
        {
            "quantity": 1,
            "variantId": "gid:\/\/shopify\/ProductVariant\/50677756514056",
            "productId": "gid:\/\/shopify\/Product\/9639311737333",
            "unitPrice": "39.95"
        }
    ],
    "allowDeliveryAddressOverride": true,
    "allowDeliveryPriceOverride": true
}
```

### How to Re-Run Subscriptions Export for the Fixed Orders

1. Run the SQL query for the fixed subscriptions orders
    
    ```sql
    UPDATE sylius_order
    SET
    	shopify_subscription_export_error = NULL,
    	shopify_subscription_export_status = NULL,
    	is_subscription_canceled = 0,
    	subscription_canceled_at = NULL
    WHERE
    	id = XXXXXX; # ID of the fixed subscription order
    ```
    
2. Run the exporting again by any method (the “particular order exporting”, “manual bulk exporting”, “full exporting via cron job”) from the “[How To Perform Migration](Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md)” section above.

# How to revert changes in Sylius/CRM

Depending on whether the exported subscription was **paused** or **active** before exporting, the full reverting flows differ slightly.

1. For **both**, active and paused subscriptions, run in the DB:
    
    ```sql
    UPDATE sylius_order
    SET
    	shopify_subscription_export_error = NULL,
    	shopify_subscription_export_status = NULL,
    	is_subscription_canceled = 0,
    	subscription_canceled_at = NULL
    WHERE
    	id = XXXXX; # ID of the subscription order to revert
    ```
    
2. Check if the subscription was (is) paused:
    
    ```sql
    SELECT subscription_paused FROM sylius_order WHERE id = XXXXX;
    ```
    
3. If the subscription is **paused**, run the additional query that fully restores the corresponding “resume_subscription” and “notify_admin_resume” tasks for the subscription:
    
    ```sql
    UPDATE sylius_order_tasks
    SET
    	executed = 0
    WHERE
    	executed = 1
    	AND executed_at_time IS NULL
    	AND task_type IN ('notify_admin_resume', 'resume_subscription')
    	AND order_id = XXXXX;
    ```
    
4. **Optionally**, remove the corresponding subscription on Shopify/Appstle side. You can see the Shopify Subscription ID in
    1. The “sylius_order_action_history” DB table
        
        ```sql
        SELECT * FROM sylius_order_action_history WHERE order_id = XXXXX;
        ```
        
    2. The “shopify_exported_subscription” DB table
        
        ```sql
        SELECT * FROM shopify_exported_subscription WHERE order_id = XXXXX;
        ```
        
5. **Optionally**, clear records about the previously exported subscriptions from the “shopify_exported_subscription” DB table:
    
    ```sql
    DELETE FROM shopify_exported_subscription WHERE order_id = XXXXX;
    ```
    

# Notable SQL Queries

## Query to test subscriptions - which ones to manually move

[(c) Karolis in Slack](https://malaberg.slack.com/archives/D06TJSAK8DU/p1741974858775009?thread_ts=1741899518.421959&cid=D06TJSAK8DU)

```sql
SELECT * FROM sylius_order o where is_subscription=1 and is_subscription_canceled=0 and state="fulfilled" and last_reorder_at < '2025-02-14' and subscription_frequency_in_days=30
  and subscription_paused = 0
  AND EXISTS (
      SELECT 1
      FROM sylius_payment p
      WHERE p.order_id = o.id
        AND p.method_id = 1
  )
;
```

## Code to find all subscriptions to export

```sql
SELECT * FROM sylius_order o where is_subscription=1 and is_subscription_canceled=0 and state="fulfilled" 
  and subscription_paused = 0
  AND EXISTS (
      SELECT 1
      FROM sylius_payment p
      WHERE p.order_id = o.id
        AND p.method_id = 1
  );
```

## Validated exported bundles inside Shopify to match warehouse units

```sql
SELECT
	o.*
FROM
	sylius_order o
	INNER JOIN sylius_order_item oi ON o.id = oi.order_id
	INNER JOIN sylius_product_variant pv ON oi.variant_id = pv.id
	INNER JOIN sylius_product p ON p.id = pv.product_id
WHERE
	oi.quantity > 0
	AND p.shopify_is_bundle = 1
	AND o.shopify_subscription_export_status IN ('EXPORTED', 'ORIGINAL_ORDER_INFO_ADDED');
```

## Move subscriptions which were not moved as it wasn’t fulfilled

```sql
SELECT * FROM prod_ypn.sylius_order where shopify_subscription_export_status is not null and 
shopify_subscription_export_status NOT IN ('EXPORTED', 'ORIGINAL_ORDER_INFO_ADDED') and shopify_subscription_export_error not like '%paypal_v2%'
and shopify_subscription_export_error not like '%klarna_checkout%'
```