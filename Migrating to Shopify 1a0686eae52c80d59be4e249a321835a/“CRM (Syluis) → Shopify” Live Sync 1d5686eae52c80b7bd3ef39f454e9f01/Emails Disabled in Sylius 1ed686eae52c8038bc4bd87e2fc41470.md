# Emails Disabled in Sylius

Taken from **config/packages/_sylius_mailer.yaml**

When “**Live Sync with Shopify**” is enabled in the Channel configuration in the Sylius admin UI, the sending of the following emails is **disabled** (highlighted in orange).

**In blue bold text highlighted emails whose disabling is questionable at the moment.**

---

### **🛒 Shop / Payments**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| subscription_canceled | Disabled (for Stripe) | Stripe subscriptions are cancelled immediately after export to Shopify. But these emails will be sent for PayPal subscriptions. |
| subscription_payment_failed | Kept | Only PayPal remains in Sylius, and it’s still managed there. |
| payment_update_reminder | TBD. | Might need disabling if payment method updates are moved to Shopify. |
| subscription_payment_failed_expired_card | Kept | Shopify handles subscription cancellations, no email needed from Sylius. |
| subscription_payment_failed_action_required | Kept | Only sent before export to Shopify; still useful. |
| subscription_paypal_canceled | Kept | PayPal subscriptions are managed in Sylius. |
| subscription_confirmation | Disabled (for Stripe) (Verify) | Stripe subscriptions are exported; Shopify handles confirmations. |
| sales_funnel_payment_failed | Kept | Part of Sylius core functionality, still relevant. |
| paypal_initial_payment_failed | Kept | PayPal still fully managed in Sylius. |

### **💸 Shop / Refund**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| return_initiated | Kept | Not currently used, but returns are not yet synced to Shopify. |
| return_package_received | Kept | Same as above. |
| units_refunded | Disabled | Refunds are exported to Shopify; Shopify should handle notifications. |

### **📦 Shop / Order**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| order_confirmation | Disabled | Shopify handles this notification. |
| order_confirmation_resent | Disabled | Shopify handles this notification. Also admins should resend the emails from Shopify. |
| order_subscription_reminder | Disabled (for Stripe), Kept (for PayPal) (Verify) | Exported Stripe subscriptions are cancelled in Sylius. Shopify sends reminders. |
| order_invoice | Kept (Verify) | Used for abandoned carts; business logic needs confirmation. |
| customer_new_card | Disabled | Should be handled in Shopify; affects future orders only. |

### **👤 Shop / Account**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| new_shop_user_created | Disabled | Emails should be sent from Shopify |
| user_registration | Disabled | Customer interaction moves to Shopify. |
| password_reset | Disabled | Could still be needed for legacy Sylius access. |
| reset_password_pin | Disabled | See above. |
| reset_password_token | Disabled | See above. |
| verification_token | Disabled | Tied to registration, now handled in Shopify. |

### **🚚 Shop / Shipment**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| shipment_confirmation | Disabled | Shipments are managed and communicated via Shopify. |

### **🧑‍💼 Admin / Shipment**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| shipment_rejected | Kept | Internal admin use only. |

### **✉️ Shop / Contact Us**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| contact_request | Kept | Requested by Karolis. |
| mango_sylius_contact_form_contact_form_email | Kept | Requested by Karolis. |
| mango_sylius_contact_form_answer_mail | Kept | Requested by Karolis. |

### **✉️ Admin / Contact Us**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| mango_sylius_contact_form_admin_notice_mail | Kept | Requested by Karolis. |

### **📬 Admin / Invalid Address**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| admin_user_address_invalid | Kept | Still useful internally. Shopify replacement can be explored. |

### **⭐ Admin / New Product Review**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| admin_new_product_review | Kept | Not clearly used, but doesn’t conflict with Shopify integration. |

### **⚙️ Admin / Miscellaneous**

| **Email** | **Action** | **Notes** |
| --- | --- | --- |
| admin_notify_resumed_subscription | Kept | Internal use only. |
| admin_notify_warehouse_hold | Kept | Internal use only. |
| admin_max_refunds_reached | Kept | Internal use only. |
| admin_order_manual_review_needed | Kept | Internal use only. |
| admin_orders_of_aggregated_shipment_put_on_hold | Kept | Internal use only. |
| admin_possible_duplicate | Kept | Pure admin automation; still useful. |
| admin_cancel_active_subscription | Kept | Still relevant for PayPal/legacy subscriptions. |
| admin_order_with_no_shipment | Kept | Not used but still internal. |
| admin_refunded_with_active_subscription | Kept | Exported subs are cancelled, but this is useful internally. |
| admin_paypal_dispute_created | Kept | Webhook-driven alert for PayPal disputes, still handled in Sylius. |