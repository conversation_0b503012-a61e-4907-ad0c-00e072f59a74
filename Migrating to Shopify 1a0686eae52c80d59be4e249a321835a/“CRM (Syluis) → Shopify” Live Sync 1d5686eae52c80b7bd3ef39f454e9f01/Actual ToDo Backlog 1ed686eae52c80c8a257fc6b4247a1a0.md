# Actual ToDo / Backlog

- **Legend**
    
    <aside>
    💡
    
    **Status**:
    
    - green - done
    - red - cancelled
    - yellow - a temp flag. Fixed but the fix is not committed yet.
    
    **Importance**:
    
    - 0 - cancelled
    - 5 - “nice to have” tasks. Can be implemented after deployment to prod
    - 10 - important “nice to have tasks” (non blocking for deployment to prod)
    - 20 - should be implemented before deployment to prod, otherwise the live sync will require additional control, agreements, management
    - 30 and above - blockers, the app cannot be deployed to prod with these task not accomplished
    </aside>
    

| **#** | ToDo Item | Importance | Est. in hours | Comments from Karolis | Resolution |
| --- | --- | --- | --- | --- | --- |
| **1** | **Create a Production Slack notification channel**
    ◦ Alternatively, appropriately rename the existing testing one. | 10 | 1 |  | **Done**
        ▪ Added the sole channel [https://malaberg.slack.com/archives/C08KN54FSNM](https://malaberg.slack.com/archives/C08KN54FSNM)
        ▪ Its webhook is put into a general .env (any time in the future - any site can get its own channel/webhook in the .env.prod, or .env.local files) |
| **2** | **Improve Slack notifications for Live Sync errors**
    ◦ Add a link to the order page in Sylius.
    ◦ Add a note that after fixing the issue, the order can and should be (re)exported to Shopify via the button in the admin UI. | 10 | 0.5 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **3** | **Fix refunds exporting behavior for partial refunds**
    ◦ Case: 1 item of *Dark Spot Vanish* for $29.99 is ordered and exported to Shopify.
    ◦ Two refunds are made:

        1. $1 refund passes.
        2. Second $1 refund fails because Shopify considers the item already refunded (refundLineItems.0.quantity issue). | 10 | 3 | This is possible (2% of cases or less) - most of the time when customer service makes mistake in refunds. Maybe we push second refund without item? | Fixed. Implemented the second refund without items (if we have such error from Shopify).

**Warning**! We still have an issue here (maybe rare, if we have this case at all): The case:
- A customer has purchased 1 item of the product X from our site (Sylius) for $29.99
- For some reason (for example due to delay with fulfillment - we don’t have enough stock in warehouse), our Customer Support decided to refund a part of the payment made by the customer - equal to $10, made the refund and the refund has been exported to Shopify

= As a result - in Shopify this item will be shown as “removed” - and hence it will be not possible to fulfill it with Shopify.

Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **4** | **Create a console command to check Live Sync queue hourly, which**
    ◦ Checks for any orders scheduled for Live Sync that remain unsynced.
    ◦ Sends a Slack notification to devs (and/or admins) if such orders exist. | 30 | 5 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **5** | **Create a console command to populate the shopifyExportedSubscription field in Orders**
    ◦ Set it based on the corresponding ShopifyExportedSubscription ID.
    ◦ Without it, the link to the order’s subscription in the Sylius admin UI breaks.
    ◦ Not a critical bug, but important for UX. | 30 | 2 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **6** | **Implement manual triggering of refunds export in Sylius admin UI**
    ◦ Needed for cases where Live Sync fails or is interrupted. | 30 | 3 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **7** | **Display refund export status in the Shopify block of Order Details page (in Sylius admin)**
    ◦ Show exported refunds if present.
    ◦ **Potential difficulties:**
        ▪ Refunds exported during historical order recording do not have shopifyId values.
        ▪ Therefore, they will appear as “not exported”, and the entire order export status may be shown as “not fully exported”. | 10 | 1 | Historic order display “don’t matter” as much, so we don’t have to spend much/any time here. The live sync would be good to show | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **8** | **Enable manual export of individual products from Sylius admin UI**
    ◦ Needed when a product is added in Sylius but not yet used in any order, but we need it in Shopify (e.g., for discounts and subscription setup).

Upd: Since we group and sort products to the “grouped” and “bundle” products, it’s impossible (in most cases) to export individual product - as result, we usually export a bunch of Sylius products as a single product in Shopify.
For these reasons (and due to a low importance of this task) the most efficient way is (something like) to add an “Export Products” button to (for example) Sync Configuration (or Export Statistic) pages in the Shopify section of Sylius admin ui. | 5 | 2 | That won’t happen. As products which are displayed in store (and need discounts) will be created in Shopify first. Don’t think we need it. If it’s simple maybe add it, but bottom of the list | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| **9** | **Add Shopify-related info and links to products and product variants in Sylius admin UI**
    ◦ Product admin pages: show a link to the corresponding Shopify product.
    ◦ Product variant pages: show a link to the corresponding Shopify variant. | 10 | 2 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 10 | **Verify Shopify customer notifications for exported orders and refunds**
    ◦ Especially check if the refund notification emails contain sufficient information.
    ◦ Compare them with Sylius emails. | 0 | 2 | Shopify allows to customize emails with fields, so this will be all good. No action here |  |
| 11 | **Verify that refunds won’t be auto-refunded again by Shopify after export**
    ◦ Assumption: no auto-refund occurs, as we don’t export actual payment data (except for subscription-related payments).
    ◦ Needs to be tested.
**Some related notes:**
    ◦ [Shopify manual – Refunding orders](https://help.shopify.com/en/manual/fulfillment/managing-orders/refunding-orders)
    ◦ [Shopify Dev – Processing Payments](https://shopify.dev/docs/apps/build/payments/processing#refunds)
    ◦ Refunds created with transaction(kind = refund) may be interpreted by Shopify as “already processed”. Needs testing.
**Related Slack thread**: https://malaberg.slack.com/archives/D06TJSAK8DU/p1746868814827029 | 50 | 1 |  | Verified: https://malaberg.slack.com/archives/D06TJSAK8DU/p1749393587661979. Shopify does not perform refunds using the payment data we export now. |
| 12 | **Fix the method in the sylius-shopify-sync-plugin’s ShopifyAdminUiLinksBuilder which generates Shopify admin UI urls**
    ◦ Currently uses a random enabled channel from ShopifySync configuration.
    ◦ This must be corrected to use the correct channel once full multichannel functionality is implemented. | 0 | 0.5 | Not important. We only have 1 store with two channels (and because we rebranded and changed domains, than kept both domains live). We are decommissioning the old domain which will make 1 channel in use. Don’t expect to have multi channels later either |  |
| 13 | **Disable “Export Full Order Data” button for ineligible orders**
    ◦ Example: an order that is not paid yet. | 10 | 0.5 |  | Nope. If anybody will try to export an order, which is not eligible - their will get an error flash message with detailed description of why the order is not eligible for exporting to Shopify yet.

This current behavior, I believe (Mykolay), is more UX friendly than just hiding the export button without any explanations. |
| 14 | **Display a warning in the Shopify section if the order is a subscription paid via a non-Stripe method**
    ◦ Prevents confusion when the order status shows “Not exported yet” without explanation. | 10 | 0.5 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 15 | (should be tested)
**Multichannel full functionality (for products and customers)**
    ◦ It’s currently possible to export Customers and Products to the Shopify store for only one channel.
    ◦ If we have two channels and configure two Shopify stores (one for each Sylius channel), we cannot export Customers and Products to both channels at this time. (Orders can technically be exported, but doing so doesn’t make sense without associated customers and products). We have to choose just one channel for exporting.
    ◦ The sole benefit of the current multichannel functionality (which already exists) is the ability to export orders from a specific channel, whereas previously, orders from all channels were exported to a single Shopify store (e.g. in *Origins Diet* / *Aeons* – we would export all orders from the DB to the same Shopify shop).
    ◦ **Work Breakdown Structure of the needed changes:**
        ▪ Add tables shopify_customer, shopify_product where we store the relation “channel – customer” and “channel – product” for the exported products.
        ▪ Refactor all functionality that relies on Shopify data directly in Customer and Product entities/DB tables. | 5 | 3 | For Aeons/Origins - both channels need to be exported to the same store. That is important to work as Aeons in 2nd in live to be migrated

Additional comments in Slack: https://malaberg.slack.com/archives/D06TJSAK8DU/p1746869184622789 |  |
| 16 | **Find ways to send the same emails from Shopify that we currently send from Sylius**
    ◦ [Asana reference task](https://app.asana.com/1/1203063732010439/project/1207569183609754/task/1210199008880922) | 0 | 8 | Not needed |  |
| 17 | **Disable buttons in Sylius Admin UI that triggers sending specific emails for the exported orders**
    ◦ Or just put a note, that the email won’t be sent because it’s disabled in case the Live Sync is enabled? | 5 | 2 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 18 | **Warn admins if the payment method is changed on a Shopify-exported order**
    ◦ In case the payment method was updated for the order (e.g. after an admin has sent the “payment method update” email to the customer) - we should, maybe, show a warning to the Admin about the need to update the saved payment method on Shopify, especially if the orders is a Stripe subscription (which should use this new payment method)
    ◦ Alternatively - we could prohibit sending of such emails and changing payment methods on Sylius side, if the order is exported to Shopify, and it’s a Stripe subscription. We could/should have the related functionality on the Shopify side. | 20 | 2 | Use the alternative approach. Change payment method is only applicable for subscriptions, which will be migrated to Shopify | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 19 | **Notify admins if the payment method from the exported order differs from the customer’s current payment method**
    ◦ Notify via Slack or email.
    ◦ Also, consider adding a button:
        ▪ “Sync this payment method to Shopify”
        ▪ Or: “Set this payment method as the main one in Sylius and Shopify” |  |  | Based on above, don’t think this is applicable? | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 20 | **Check for compatibility issues between Live Sync and existing mass-export functionality**
    ◦ Example: cron jobs that export all orders, products, or customers.
    ◦ Confirmed: they **should** run smoothly in parallel with Live Sync, but this still needs testing. | 20 | 3 |  |  |
| 21 | **Adjustments of the emails handling**
(accordingly to review notes from Karolis in the @[Emails Disabled in Sylius](Emails%20Disabled%20in%20Sylius%201ed686eae52c8038bc4bd87e2fc41470.md) list)
- Disable for all payment methods and all subscriptions
    - payment_update_reminder
    - subscription_confirmation
    - order_subscription_reminder
    - password_reset
    - reset_password_pin
    - reset_password_token | 30 | 1 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 22 | ((c) Karolis) We have to **decide if we sync “abandoned cart” / failed orders to shopify or if we keep in Sylius**. We will need to assess where it’s better for customer to restore order.
    Depending on the above, besides the others, we have to handle the abandoned emails accordingly (keep them in Sylius or move to Shopify) | 20 | 5 |  |  |
| 23 | Some **subscription issue (video in Loom)** (Getting an issue on an existing customer sub error message):
- https://www.loom.com/share/518275290be045d5a34529af7db52a98?sid=6fdad7af-f092-47cf-9ca7-a53625530c81
- https://malaberg.slack.com/archives/D06TJSAK8DU/p1743769284671619
 | 30 | 3 | ((c) Karolis) We only had 1 user like that. So it's before or after sync. It will be blocker to enable live sync
 |  |
| 24 | **Check if Reach payment in orders created by admin trigger the “sylius.order.success” event** (and hence - trigger exporting to Shopify): https://malaberg.slack.com/archives/D06TJSAK8DU/p1746821063989569 | 5 | 0.5 |  | Fixed by the commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/c11ff25760c330b7a1c8a55d880e0fd3afea3a97 |
| 25 | **For** **manual and live sync mode, disable button if order is already sent to warehouse**. https://malaberg.slack.com/archives/D06TJSAK8DU/p1746821245851029 | 20 | 1 |  | Done. Commit https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/commit/dc624ba9f4524b45643edf47dd0fe0794e834816 |
| 26 | Check (test) if we have **any issues with exporting orders paid by customers without appropriately filled address** in Stripe: https://malaberg.slack.com/archives/D06TJSAK8DU/p1746826821076429 | 30 | 3 |  | Marked a solved at the moment: https://malaberg.slack.com/archives/D06TJSAK8DU/p1749463922300339?thread_ts=1746826821.076429&cid=D06TJSAK8DU |
| 27 | Check **if any Klaviyo interactions have affected**: https://malaberg.slack.com/archives/D06TJSAK8DU/p1746823070227079?thread_ts=1746822359.082819&cid=D06TJSAK8DU | 5 | 2 |  |  |
| 28 | **Check the full flow of fulfillment in/by Shopify** of the synced orders:
- with free shippping
- with a paid shipping, paid on the Sylius side
Related conversation in Slack: https://malaberg.slack.com/archives/D06TJSAK8DU/p1746868347230989 | 30 | 3 |  |  |
| 29 | Implement a console command that safely removes old orders data (we need it to re-export orders data with taxes).
Before exporting we have to
- Adjust the order→shopifyAdjustedRequest in the DB (because if it exists - it means it was used to correctly export the order to the Shopify side)

- We should remove
    - Orders
         - From Shopify (their order items, transactions (payments and refunds) will be automatically removed
         - In the CRM DB we should clear
            - order
                - shopifyId = null
                - shopifyRequest = null
                - shopifyUpdateNeedeed = null
                - shopifyPlannedRequest = null
                - shopifyErrors = null
                - shopifyPreExportValidationErrors = null
                - shopifyPreExportFixPolicy = null
                - shopifyPreExportValidated = false
                - shopifyPullFlag = null
                - shopifyLiveSyncStatus = null
                - shopifyLiveSyncScheduledAt = null
                - put the shopifySubscriptionExportStatus to SHOPIFY_SUBSCRIPTION_EXPORT_STATUS_EXPORTED
            - orderItem
                - shopifyId = null
            - payment
                - shopifyId = null
            - refundPayment
                - shopifyId = null
                - shopifyRequest = null
                - shopifyUpdateNeeded = false
                - shopifyPlannedRequest = null
                - shopifyErrors = null
                - shopifyPreExportValidationErrors = null
                - shopifyPreExportFixPolicy = null
                - shopifyPreExportValidated = false
                - shopifyAppClientId = null

- We can keep untouched
    - Products
    - Customers
    - Customers Stripe Payment data
    - Subscriptions |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |