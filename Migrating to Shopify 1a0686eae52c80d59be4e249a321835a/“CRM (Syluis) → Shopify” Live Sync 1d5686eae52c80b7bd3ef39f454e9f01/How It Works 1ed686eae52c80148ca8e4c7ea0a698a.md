# How It Works

# **How It Works**

## **Live Sync (and Manual) vs Historical Orders Exporting**

In opposite to the [Migrating of Historical Data](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md),

- **Live Sync exporting** is triggered by the sylius.order.success event, which occurs once the order is fully paid.
- **Manual exporting** is triggered by admins on the Order’s view page → **Shopify Sync** section (bottom of the right sidebar) by clicking the **Export full order data to Shopify** button.

In both Live Sync and Manual modes, the export process includes:

- Immediately mark the order as syncedWithWarehouse to prevent sending it to warehouses (since fulfillment is handled by Shopify).
- Export full order data to Shopify:
    1. Export the order’s products (if not already exported).
    2. Export the order’s customer (if not already exported).
    3. Export the order itself.
    4. Prevent the order from being sent to the warehouse from Sylius.
    5. Mark the order as shipped immediately after export.
    6. If the order was paid with Stripe:
        1. Export payment method details to Shopify (for the customer).
        2. If it’s a subscription order:
            - Export the subscription to Shopify / Appstle subscription app.
            - Cancel the subscription in Sylius immediately after export (to prevent recurring orders).
- Disable all Sylius emails related to the newly created customer or order (we assume Shopify will handle those emails).

### **Data Exported**

- **Customers and products**: The same data is exported in both Live Sync/Manual and Historical modes.
- **Orders**: There are differences:
    - closedAt:
        - null in Live Sync and Manual modes (to avoid marking the order as archived).
        - order->getCheckoutCompletedAt() in Historical mode (to mark the order as archived).
    - **Fulfillment, fulfillment status, shipping lines**:
        - Not sent in Live Sync mode (otherwise Shopify auto-marks as fulfilled).
        - Sent in Historical mode to mark the order as fulfilled.
    - **Tags**:
        - "LiveSync" in Live Sync and Manual modes.
        - "Migrated" in Historical mode.
    
    ---
    

## **Refunds Exporting**

- Refund exporting is triggered in the method SyliusRefundRefundPaymentCallback::syncRefund() after the complete transition in the sylius_refund_refund_payment state machine — i.e., when the refund is fully processed.

### Data Exported

In the refund data sent to Shopify:

- Include a refund note containing:
    - CRM RefundPayment ID
    - Initiator
    - Support case URL
    - Refund reason
- Request Shopify to send a customer notification.
- Link the refund to the order using the order’s shopifyId.
- Summarized RefundItems (sylius_refund_refund) into:
    - lineItems: aggregate all items of type *order_item_unit* based on the original purchased items.
    - shipping: aggregate all (typically one) items of type *shipment* into the refunded shipping data.

> ⚠️ We must know the shopifyId of each originally exported *order item*; otherwise, we can’t accurately map which items were refunded.
Now we have these shopifyId automatically once the order is exported to Shopify. But for the exported historical orders - we did not have such data saved yet, for this reason - we have the *BulkPullOrderDataFromShopifyCommand* which can be run from CLI and will pull the needed data from Shopify for all previously exported orders.
> 
- Map the RefundPayment to a transaction (single-item list) with type "refund":
    - Set the gateway to Manual ({refundPayment->getName()}).
    - Provide the parentId — the shopifyId of the original payment transaction being refunded.

> Alternatively, the gateway could be 'store-credit', 'exchange-credit', or 'cash', which wouldn’t require a parentId, but would not reflect the actual payment flow.
> 

## Recurring Check for issues of Live Sync process

There is a console command `malaberg_sylius_shopify_sync_plugin:check_live_sync_status`, which should be put on a hourly (or daily), which checks:

- if any orders or refunds got

---

> In both full order and refund exports, if an exception occurs, a notification is sent to the Slack channel: https://malaberg.slack.com/archives/C08KN54FSNM
> 

> Live sync for both orders and refunds runs asynchronously via MessageHandlers (the “*shopify*” transport).
> 

---

# **How to Use**

(Also see the [Installation and Configuration of Live Sync](Installation%20and%20Configuration%20of%20Live%20Sync%201f2686eae52c8044b289ef7a15ca6aa0.md) page for the instructions on how to install and enable Live Sync in our Sylius storefronts.)

### **Order Page Overview**

Once Live Sync is enabled, the **Order View** page will show a **Shopify** section at the bottom of the right sidebar. This section displays export statuses for the order’s related information:

- Products
- Customer
- Order
- Payment method
- Subscription

If an item was exported successfully, a link to the relevant Shopify page is shown. If an error occurred, it will be displayed next to the corresponding item.

If the order is not fully exported, a **Export full order data to Shopify** button will be visible. Clicking it safely retries the export — previously exported data will not be duplicated, and missing or failed exports will be reattempted.