# Installation and Configuration of Live Sync

# Installation

1. Merge MRs (the actual final links may vary):
    1. CRM-Core: [https://gitlab.com/samtos/crm-core/-/merge_requests/461](https://gitlab.com/samtos/crm-core/-/merge_requests/461)
    2. Storefronts:
        1. DSS: [https://gitlab.com/malaberg-crm/doctor-sister/-/merge_requests/396](https://gitlab.com/malaberg-crm/doctor-sister/-/merge_requests/396)
        2. Apex Labs: [https://gitlab.com/malaberg-crm/apex-labs/-/merge_requests/330](https://gitlab.com/malaberg-crm/apex-labs/-/merge_requests/330)
2. (optional) Put a custom SLACK_WEBHOOK_URL_SHOPIFY_SYNC_NOTIFICATIONS value to the .env file(s)
    
    At the moment we use the sole one for all sites (the channel in Slack named “shopify-live-sync”):
    
    ```
    SLACK_WEBHOOK_URL_SHOPIFY_SYNC_NOTIFICATIONS=*******************************************************************************
    ```
    

3. For all already configured in Sylius Shopify Apps, set the **Channel** value in **Sylius admin UI -> Shopify -> Apps Configuration**.

4. Configure the Live Sync in Sylius admin UI (see the [Configuration](Installation%20and%20Configuration%20of%20Live%20Sync%201f2686eae52c8044b289ef7a15ca6aa0.md) section below)

5. Run a new messenger consumer for the “**shopify**” transport (put it to supervisor config):

```
bin/console messenger:consume shopify --limit=10 -time-limit=3600

```

6. *(Optionally. But without this - it won’t be possible to live sync refunds that are created from orders that have been exported previously)* Run the `BulkPullOrderDataFromShopifyCommand` to pull orders’ transactions and line items data from Shopify to previously exported (historical) orders:

a) Run the SQL query:

```
UPDATE sylius_order SET shopify_pull_flag = 1 WHERE shopify_id IS NOT NULL AND id > 0;
```

b) Put to the every minute cron (similarly to other bulk shopify commands):

```
* * * * * bin/console malaberg_sylius_shopify_sync_plugin:bulk_pull_order_data_from_shopify CHANNEL_CODE -b 100
```

# Configuration

To enable **Live Sync**:

1. In **Shopify → Apps Configuration** (Sylius Admin UI), enable at least one Shopify App for the channel where you’re going to enable Live Sync with Shopify.
2. In the **Shopify Sync** section on the **Edit Channel** page, fill in:
    - **Shopify shop domain**
    - **Shopify admin UI ID**
    - **Appstle API Key**
3. Check the **Enable Live Sync with Shopify** checkbox.
4. (Optional) In **Shopify → Sync Configuration**, you can:
    - Enable/disable logging of export steps into the DB.
    - Show/hide the **Synced with Shopify?** column in the Products, Customers, and Orders grids.

1. In Shopify admin UI → Settings → Notifications in customer emails - replace the {{ transaction_name }} and {{ transaction.gateway_display_name }} with “&nbsp;” in case the payment method is not Gift Card or Shopify Pay, as it’s done in the task https://app.asana.com/1/1203063732010439/project/1210420287041318/task/1210483986005967. to prevent exposing to a customer our “weird” migrated payment method name like “manual (stripe checkout_v3)”.
1. Configure and commit redirects configuration from Sylius to Shopify, as described in the task https://app.asana.com/1/1203063732010439/project/1210484022385514/task/1210525547695112