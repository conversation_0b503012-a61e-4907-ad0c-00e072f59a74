# Migrating to Live Sync from the Previous Version

Since we have introduced taxes exporting in orders data, we need to remove the previously exported historical orders and re-export them back to Shopify (because Shopify does not allow to update taxes for orders that are already exported).

# Migration Flow

During the migration steps below, we’re adjusting the following data

- **In the “data deletion” step**
    - **on the Shopify side**
        - **Delete**
            1. Order (and, hence, all the order’s transactions, refunds, shipments will be deleted automatically by Shopify)
        - **Do not delete** the following data related to the deleted order
            1. Customer
            2. Product
            3. Subscription (we just will replace the subscription’s “origin order” data after we will re-export the deleted order back to Shopify)
    - **On the Sylius side**
        - **Delete** (clear, set to NULL) the following Shopify data in Sylius DB
            1. Order
                1. shopifyId
                2. shopifyRequest
                3. shopifyUpdateNeeded
                4. shopifyPlannedRequest
                5. shopifyErrors
                6. shopifyPreExportValidationErrors
                7. shopifyPreExportFixPolicy
                8. shopifyPreExportValidated
                9. shopifyPullFlag
                10. shopifyLiveSyncStatus
                11. shopifyLiveSyncScheduledAt
            2. OrderItems
                1. shopifyId
            3. Payments
                1. shopifyId
            4. RefundPayment
                1. shopifyId
                2. shopifyErrors
                3. shopifyRequest
                4. shopifyUpdateNeeded
                5. shopifyPlannedRequest
                6. shopifyPreExportValidationErrors
                7. shopifyPreExportFixPolicy
                8. shopifyPreExportValidated
                9. shopifyAppClientId
        - **Kepp** (set)
            1. Order
                1. shopifyAdjustedRequest - we keep it because it’s not empty only for the orders that can be exported *only* using this adjusted request. Therefore, in step 4 below, we need to modify the shopifyAdjustedRequest values by adding the required tax information before running the bulk export.
                2. previousShopifyId - a value of the shopifyId before it was cleared
                3. previouslyDeletedFromShopifyAt - a datetime of a clearance of the Shopify data above

# Migration Steps

## Preliminary

1. Copy the “Shopify Shop URL” value from any Shopify App (as far as all of them have to same value) into some notebook (because it will be removed by the DB update, while we need this value in a new Channel’s “Shopify Shop Url” field)
2. Create a backup of the DB
3. Disable (remove or comment) the “bulk exporting to Shopify” commands if they are still running by cron.
- 4. Enable “Collecting taxes” checkbox in the Taxes and Duties section in the Shopify shop settings
    
    ![image (6).png](Migrating%20to%20Live%20Sync%20from%20the%20Previous%20Version%201f8686eae52c80feb9f8dcf2bcac94a5/image_(6).png)
    
    ![image (7).png](Migrating%20to%20Live%20Sync%20from%20the%20Previous%20Version%201f8686eae52c80feb9f8dcf2bcac94a5/image_(7).png)
    

## Installation

1. Merge MRs
    1. CRM-Core: [https://gitlab.com/samtos/crm-core/-/merge_requests/461](https://gitlab.com/samtos/crm-core/-/merge_requests/461)
    2. Storefronts:
        1. DSS: [https://gitlab.com/malaberg-crm/doctor-sister/-/merge_requests/396](https://gitlab.com/malaberg-crm/doctor-sister/-/merge_requests/396)
        2. Apex Labs: [https://gitlab.com/malaberg-crm/apex-labs/-/merge_requests/330](https://gitlab.com/malaberg-crm/apex-labs/-/merge_requests/330)
2. Run the `bin/console doctrine:schema:update --force` (if it won’t be run by the deployment script)
3. In Sylius Admin UI
    1. Enable the “Shopify Integration Management” permission for your admin user’s role (if not enabled yet)
    2. On the Channel → Edit page
        1. Disable Shopify Live Sync
        2. Put a value into the fields
            1. Shopify shop domain
            2. Shopify shop Admin URL ID
            3. Appstle Subscriptions API key
    3. On the Shopify → Apps Configuration set a correct channel to Shopify Apps (for setups with 1 channel will be enough to just press the “Save” button - the channel will be pre-selected and saved for all Shopify Apps)
4. In Shopify Partners Dashboard
    
    <aside>
    💡
    
    Without this step - Shopify will return “null” for the requested order data (for any order exported more than 2 months ago, in any requests - delete, pull, etc.)
    
    </aside>
    
    1. request the “Read all orders scope” API permission for the exporting apps (with the phrase, for instance, “We're developing a re-platforming app, and need this permission for the data migration phase.”)
    2. re-install all the (enabled) apps (to enable the granted “read_all_orders” scope for them)

## Migration to the new version (with taxes)

1. Clear the shopifyAppClientId and shopifyErrors for all orders in the DB with the query
    
    ```sql
    UPDATE sylius_order SET shopify_app_client_id = NULL, shopify_errors = NULL WHERE shopify_id IS NOT NULL AND id > 0;
    ```
    
2. Delete all exported orders from Shopify: put the `BulkDeleteExportedOrderFromShopifyCommand` to cron as the (something like) following:
    
    ```sql
    * * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:bulk_delete_exported_order_from_shopify {CHANNEL_CODE} -b 100
    ```
    
3. Once the command above processed all orders (you can verify it by checking if there is remaining not deleted orders by the sql query
    
    ```sql
    SELECT count(*) FROM sylius_order WHERE shopify_id IS NOT NULL AND shopify_errors IS NULL;
    ```
    
    which should return 0 if the command above is finished the processing), please, check if any orders have obtained errors during the deletion:
    
    ```sql
    SELECT count(*) FROM sylius_order WHERE shopify_id IS NOT NULL AND shopify_errors IS NOT NULL;
    ```
    
    If there are any errors - please, fix them as the error description suggests, and either
    
    - start with the step 1 above (from deleting shopifyErrors and shopifyAppClientId) - hence the app will re-try the orders deletion process
    - or adjust the bulk orders deletion command with the flag `--force` - all shopify data of the orders with errors will be deleted from the local (CRM) DB.
4. Once the orders will fully disappear from the Shopify site:
    1. Remove (or comment out) the deletion cron command from the above from cron
    2. Adjust the “**shopify_adjusted_request**” of the orders in the DB (add a needed taxes data to the request) to enable us to correctly export the orders back to Shopify (because the “shopify_adjusted_request” was created and filled for orders, which had exporting errors and can be fixed only by putting a value to the “shopify_adjusted_request” field).
        
        <aside>
        💡
        
        Use the [Malaberg: Shopify Order Tax JSON Fixer](https://www.notion.so/Malaberg-Shopify-Order-Tax-JSON-Fixer-********************************?pvs=21) custom GPT to add taxes automatically to the JSON taken from the saved “shopify_adjusted_request” values.
        
        It will add taxes information by following [**Order** level](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) tax-related rules.
        
        </aside>
        
5. Re-export the orders back to Shopify: put to cron the bulk orders exporting command (see [**Export Orders**](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) [](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md)for details).
6. If Subscriptions have not been exported yet to Shopify - we should export them by following the guides
    1. [Preparation for Migration](../Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md) 
    2. [How to Perform Migration](../Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md) 
7. Once the re-exporting of the orders data is finished, we need to update the “original order data” of the exported Appstle Subscriptions (with the new Shopify data of our orders). For this, put to cron the `AppstleAddOriginalOrderInfoToSubscriptionContractCommand` command as it’s described in the [Full export (put to the cron)](../Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md) section.
8. Verify the orders re-exporting results as it’s described in the sections
    1. [**Check Orders for Exporting Issues**](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) 
    2. [How To Verify Migration Correctness](../Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) 

## Post Migration Steps

Enable live sync functionality with the following steps

1. Put the `bin/console messenger:consume shopify --limit=100 --time-limit=3600` to the consumers’ supervisor config
2. Put to a hourly (daily?) cron the `CheckLiveSyncStatusCommand` command (as a “heartbeat checker” of the live sync functionality):
    
    ```sql
    0 * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:check_live_sync_status {CHANNEL_CODE}
    ```
    
3. Enable “Live Sync with Shopify” in the Channel → Edit page of the Sylius admin UI
4. Configure the Shopify Sync as you wish on the Shopify → Sync Configuration page (but preferable is to enable all checkboxes to have all Shopify data shown in UI for the exported entities).

1. In Shopify admin UI → Settings → Notifications in customer emails - replace the {{ transaction_name }} and {{ transaction.gateway_display_name }} with “&nbsp;” in case the payment method is not Gift Card or Shopify Pay, as it’s done in the task https://app.asana.com/1/1203063732010439/project/1210420287041318/task/1210483986005967. to prevent exposing to a customer our “weird” migrated payment method name like “manual (stripe checkout_v3)”.

# Important Notes

- After deletion and re-exporting of orders, the numbers in Shopify reports will be wrong for some time (maybe even for two weeks) due to a lag in Shopify’s stats’ cache revalidation.