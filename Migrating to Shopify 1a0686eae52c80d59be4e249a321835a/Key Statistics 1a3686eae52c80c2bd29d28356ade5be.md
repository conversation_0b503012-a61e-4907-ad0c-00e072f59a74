# Key Statistics

# Most Importand Indicators

## Shopify Analytics

Go to **Shopify Storefront Admin UI → Analytics → Reports → New exploration** and put the following ShopifyQL code to the editor textarea:

```sql
FROM sales
SHOW orders, gross_sales, discounts, returns, net_sales, shipping_charges, total_sales, taxes
```

## Orders Total

See “**Store Level** → **Gross Indicators** → **Orders Total**” below

## Net Sales

See “**Store Level → Net Indicators →** **Net Sales**” below

# Store Level

**Shopify’s data** can be retrieved utilizing ShopifyQL queries in the “Analytics → Reports” section of a Shopify store’s admin UI:

- [https://shopify.dev/docs/api/shopifyql](https://shopify.dev/docs/api/shopifyql)
- [https://help.shopify.com/en/manual/reports-and-analytics/shopify-reports/report-types/shopifyql-editor](https://help.shopify.com/en/manual/reports-and-analytics/shopify-reports/report-types/shopifyql-editor)
- [https://www.shopify.com/retail/gross-sales](https://www.shopify.com/retail/gross-sales)

**Sylius data** we retrieve by performing SQL queries on the Sylius store’s MySQL DB.

## Gross Indicators

- **Gross Sales** = Sum of Subtotals of all orders = Sum(each order sum(each order_item(unit_price * quantity)))
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47743306](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47743306)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW gross_sales
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                CONCAT('£', FORMAT(SUM(o.items_total) / 100, 2)) AS gross_sales
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
                oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            ```
            
- **Gross Discounts** = Sum of all orders discounts
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47808842](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47808842)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW discounts
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                CONCAT('£', FORMAT(SUM(o.adjustments_total) / 100, 2)) AS gross_discounts
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
            	  o.adjustments_total < 0
                AND oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            
            ```
            
- **Gross Shipping** = Sum of shipping costs for all orders
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47841610](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47841610)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW shipping_charges
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                CONCAT('£', FORMAT(SUM(o.adjustments_total) / 100, 2)) AS gross_discounts
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
            	  o.adjustments_total > 0
                AND oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            ```
            
- **Gross Taxes** = 0. We do not use taxes accounting now.
- **Gross Refunds** = Sum of all refund payments
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/********](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/********)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW gross_returns
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                CONCAT('£', FORMAT(SUM(r.amount) / 100, 2)) AS gross_refunds
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_refund_payment r
            		ON o.number = r.order_number
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
            	  r.state in ('Completed')
                AND oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            
            ```
            
- **Gross Payments** = Sum of all payments made by customers
    - Shopify’s calculation:
        - Example report: (haven’t found in Shopify yet. Something related is “total_sales” from “sales”, but the result in several times bigger than the “gross_sales” what is strange.
        - ShopifyQL query:
            
            ```sql
            TBD
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                CONCAT('£', FORMAT(SUM(p.amount) / 100, 2)) AS gross_payments
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_payment p
            		ON o.id = p.order_id
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
            	  p.state in ('completed', 'refunded', 'partially_refunded', 'authorized')
                AND oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            ```
            
- **Orders Total** = Amount of orders in all statuses
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47874378](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47874378)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW orders
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT 
                count(distinct o.id) AS gross_orders
            FROM 
                prod_dss.sylius_order o
                INNER JOIN prod_dss.sylius_order_item oi 
                    ON o.id = oi.order_id
            WHERE 
            	oi.quantity > 0
                AND (
                    (o.state IN ('new', 'fulfilled') 
                     AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                    OR (o.state = 'cancelled' 
                        AND o.payment_state IN ('partially_refunded', 'refunded'))
                );
            
            ```
            
- **Customers Total** = Amount of all registered customers
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47907146](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47907146)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW customers
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT count(*) FROM prod_dss.sylius_customer;
            ```
            

## Net Indicators

- **Net Sales** = Gross Sales - Gross Discounts + Gross Shipping + Gross Taxes - Gross Refunds
    - Shopify’s calculation:
        - Example report: [https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47907146](https://admin.shopify.com/store/pre-production-ypn/analytics/reports/47907146)
        - ShopifyQL query:
            
            ```sql
            FROM sales
            SHOW customers
            ```
            
    - Sylius calculation
        - MySQL query:
            
            ```sql
            SELECT count(*) FROM prod_dss.sylius_customer;
            ```
            
- **Net Payments** = Gross Payments - Gross Refunds
- **Customers with Orders** f

## Additional Shopify’s Net Indicators

# Order Level

The terms below taken from the Shopfy’s admin user UI.

Formulas taken from experiments and from Shopify’s help/info notes.

## Basic Amounts

- **Subtotal** = sum (unit price * quantity) for all items in the order
- **Shipping** = shipping cost (we always have 1 shiping line with 0 (if it’s free shippng) or bigger amount)
- **Discount** = discount amount
- **Taxes** = always 0, we do not send taxes information to Shopify
- **Paid** = sum of amounts of all transactions with type “SALE”
- **Refunded** = sum of amouts of all transactions with type “REFUND”

## Basic Shopify Formulas

- **Original Order** amount = Subtotal + Shipping + Taxes - Discount - Refunded
- **Net Payment** = Paid - Refunded

- **Refund Owed** = Net Payment - Original Order amount
- **Unauthorized Payment** (a customer owes us payment) = Original Order amount - Net Payment

Refund Owed = -Unauthorized Payment

## Order Status Calculation on Shopify

For an order to be **free of debts** - meaning neither does the customer owe us money nor do we owe the customer a refund - 

- both the "**Refund Owed**" and "**Unauthorized Payment**" **must be 0**.
- Or, in other words: **Original Orde**r amount **must be equal** to **Net payment.**
- Or, in other words: **Subtotal** + **Shipping** + **Taxes** - **Discount** - **Refunded**  = (**must be equal** to) = **Paid** - **Refunded**