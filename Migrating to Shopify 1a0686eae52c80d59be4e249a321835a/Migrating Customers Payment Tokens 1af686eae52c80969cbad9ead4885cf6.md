# Migrating Customers Payment Tokens

<aside>
💡

**Warning!** This migration **must** be performed **after** the [Migrating of Historical Data](Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md) actions performed.

</aside>

## Shopify’s Related Documentation

- [https://shopify.dev/docs/apps/build/purchase-options/subscriptions/migrate-to-subscriptions-api/migrate-customer-information#migrate-from-stripe](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/migrate-to-subscriptions-api/migrate-customer-information#migrate-from-stripe)
- https://shopify.dev/docs/api/admin-graphql/latest/mutations/customerPaymentMethodRemoteCreate
- https://shopify.dev/docs/api/admin-graphql/latest/mutations/customerPaymentMethodPaypalBillingAgreementCreate

**Other sources:**

- https://help.ordergroove.com/hc/en-us/articles/1500012883701-Setting-up-to-Migrate-to-Shopify-Payments#q-is-stripe-used-as-a-secondary-gateway-through-the-migration-only-or-for-the-life-of-the-customer-s-subscription--0
- https://loopsubscriptions.crunch.help/en/migrate-customers-and-subscriptions/migrate-customers-payment-methods-from-stripe-to-shopify
- https://learn.loopwork.co/en/article/payments-migration-gy6u7e/
- https://support.simplee.best/en/articles/6032104-connecting-stripe-in-preparation-for-a-migration

## Shopify’s Supported Payment Gateways

**Shopify supports** at the moment migrating the customers’ payment tokens **only** from

- **Stripe**, **Braintree**, **Authorize Net**: via the [customerPaymentMethodRemoteCreate](https://shopify.dev/docs/api/admin-graphql/latest/mutations/customerPaymentMethodRemoteCreate) GraphQL mutation
- **PayPal**: via the [customerPaymentMethodPaypalBillingAgreementCreate](https://shopify.dev/docs/api/admin-graphql/latest/mutations/customerPaymentMethodPaypalBillingAgreementCreate) GraphQL mutation.

## Payment Gateways We Use

In our turn, we have **Stripe**, **Klarna**, **PayPal** customers’ payment data in **our CRM DB** with the following amounts for DSS, for example:

- Stripe: **46,080** customers
- PayPal: **23,920** customers
- Klarna: **1,001** customer
- Reach: 0

- SQL to see stats for the customers payments data stored in our CRM (expand to see)
    
    ```sql
    # --- All customers ---
    SELECT count(*) FROM `prod_dss`.`sylius_customer`; # 76000
    
    # --- Customers with orders: 59224 ---
    SELECT count(DISTINCT(c.id)) FROM sylius_customer c
    INNER JOIN sylius_order o ON c.id = o.customer_id
    INNER join sylius_order_item oi on o.id = oi.order_id
    WHERE oi.quantity > 0
    AND (
    	(
    		o.`state` in ('new', 'fulfilled')
    		AND o.payment_state in ('paid', 'partially_refunded', 'refunded')
    	) OR (
    		o.`state` = 'cancelled'
    		AND o.payment_state in ('partially_refunded', 'refunded')
    	)
    );
    
    # --- Stripe ---
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE stripe_id IS NOT NULL AND stripe_id != ''; # 46080
    
    # --- Klarna ---
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_klarna_customer_token IS NOT NULL AND payment_klarna_customer_token != ''; # 1001
    
    # --- Reach ---
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_reach_contract_id IS NOT NULL AND payment_reach_contract_id != ''; # 0
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_reach_card_last4 IS NOT NULL AND payment_reach_card_last4 != ''; # 0
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_reach_stash_id IS NOT NULL AND payment_reach_stash_id != ''; # 0
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_reach_payment_method IS NOT NULL AND payment_reach_payment_method != ''; # 0
    SELECT count(*) FROM `prod_dss`.`sylius_customer` WHERE payment_reach_device_fingerprint IS NOT NULL AND payment_reach_device_fingerprint != ''; # 0
    
    # --- PayPal ---
    SELECT
    	count(DISTINCT (o.customer_id))
    FROM
    	sylius_order o
    	INNER JOIN sylius_payment p ON o.id = p.order_id
    WHERE
    	pay_pal_payment_id IS NOT NULL; # 23929
    ```
    

# Stripe Migration

## Stripe Client IDs Migration (Payment Methods / Tokens Migration)

We use the [customerPaymentMethodRemoteCreate](https://shopify.dev/docs/api/admin-graphql/latest/mutations/customerPaymentMethodRemoteCreate) GraphQL mutation to send to Shopify the Stripe Customer IDs that stored in the sylius_customer table in the stripe_id column of our CRM DB.

The exporting/migration process is as the following:

### 1. Prepare Exporting Apps

For all Exporting Apps should be enabled an additional API Access Scope “Subscriptions API”:

1. Go To “Shopify Partner Dashboard → All Apps → A particular app → API Access → Access Subscription API → Request Access”
2. In the textarea below the question “Please describe your app’s functionality in detail”, put the following text:
    
    > We are building a re-platforming solution from a custom Sylius-based CRM to Shopify. We need to migrate our customers’ payment connections (Stripe, PayPal), subscriptions.
    > 
3. In the checkbox “Are you building this app as a testing/demo app or a production app?” put “ProductIon”
4. In the checkbox “Does your Partner Organization have another app with approved Subscriptions API scopes?” put “Yes”
5. Press the “Request access” button.

### 2. Export Customers Stripe IDs to Shopify

1. Run a probe exporting for 1 (or several) customers separately:
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:export_stripe_payment_data {CHANNEL_CODE} {app_client_id} --customer_id=XXX -vvv
    ```
    
2. Manually run a bulk exporting for a small amount of customers once (2 customers per app, in this case):
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_stripe_payment_data {CHANNEL_CODE} -b 2 -vvv
    ```
    
3. Full export: put the bulk exporting command on cron:
    
    ```bash
    * * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_stripe_payment_data {CHANNEL_CODE} -b 70
    ```
    

### 3. Verify Customers Exported Stripe Data

Since Shopify checks and invalidates (if any) exported payment methods not immediately during the exporting request, but after some time (a few seconds) - we need to check an eventual status of the exported payments data by pulling it back (importing) from Shopify with the following sequence:

1. Run a probe importing for 1 (or several) customers
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:import_customers_payment_data {app_client_id} --customer_id=XXX -vvv
    ```
    
2. Manually run a bulk importing for small amount of customers once (2 customers per app in this case):
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:bulk_import_customers_payment_data {CHANNEL_CODE} -b 2 -vvv
    ```
    
3. Full import: put the bulk importing command on cron
    
    ```bash
    * * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:bulk_import_customers_payment_data {CHANNEL_CODE} -b 70
    ```
    

After the import above is done, we can verify the presence and statuses of the customers payment methods on the Shopify side by performing SQL queries on the **shopify_customer_payment_method_imported** table in the CRM DB:

- SQL:
    
    ```sql
    # The main check: How many customers with Stripe data should be exported,
    # but they do not have data on the Shopify side:
    SELECT
    	count(*)
    FROM
    	sylius_customer c
    	LEFT JOIN shopify_customer_payment_method_imported pmi ON c.id = pmi.customer_id
    	AND (
    		pmi.revoked_at IS NULL
    		OR pmi.revoked_reason = 'STRIPE_RETURNED_NO_PAYMENT_METHOD'
    	)
    WHERE
    	c.stripe_id IS NOT NULL
    	AND pmi.id IS NULL;;
    
    # How many customers data (stripe IDs) have been successfully sent to Shopify
    SELECT count(*) FROM sylius_customer c
    INNER JOIN shopify_customer_payment_method_imported pmi ON c.id = pmi.customer_id AND pmi.revoked_at IS NULL;
    
    # How many customers got errors during exporting their Stripe IDs to Shopify
    SELECT count(*) FROM sylius_customer
    WHERE shopify_stripe_export_error IS NOT NULL;
    ```
    

### Troubleshooting

1. **Edge case 1**
    
    > https://malaberg.slack.com/archives/D06TJSAK8DU/p1742230179943329 (c) Karolis:
    > 
    > 
    > There is edge case with Stripe payment tokens being moved. For example in YPN. Same customer has two profiles. The ID on sylius_customer is cus_RxEJwA37HA10Ip and it doesn't have payment method. However customer have paid for order.
    > 
    > Maybe for people which missing payment - we extract from payment on order? It's additional calls so wouldn't do for all but in priciple should be fine
    > 
    
    **Fixed by:** 
    
    > https://malaberg.slack.com/archives/D06TJSAK8DU/p1742409295549489
    > 
    > 
    > How to run it:The default "dry-run" mode (can be run with one order ID, or with a list of order IDs separated by comma):
    > 
    > > bin/console malaberg_sylius_shopify_sync_plugin:fix_stripe_customer_ids --order_ids=254706,254705 -vvv
    > > 
    > 
    > To actually apply/save the found new Stripe Customer IDs to the $customer, the "--force" flag must be added:
    > 
    > > bin/console malaberg_sylius_shopify_sync_plugin:fix_stripe_customer_ids --order_ids=254706,254705 -vvv --force
    > > 

## Stripe Subscriptions Migration

TBD

# PayPal Migration

We do not migrate PayPal payment information at the moment. Accordingly to plans, it will be done via third party subscription app (Appstle Subscriptions) only for subscriptions

# Klarna Migration

We do not migrate Klarna payment information (c) Karolis.