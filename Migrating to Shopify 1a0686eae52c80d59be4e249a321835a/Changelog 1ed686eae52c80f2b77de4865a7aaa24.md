# Changelog

# Version 1.0.0 (24.03.2025)

**GitLab**: [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/tree/1.0.0](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/tree/1.0.0?ref_type=tags)

**Implemented:**

- [Migrating of Historical Data](Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8.md)
- [Migrating Customers Payment Tokens](Migrating%20Customers%20Payment%20Tokens%201af686eae52c80969cbad9ead4885cf6.md)
- [Subscriptions Migration](Subscriptions%20Migration%201b6686eae52c809eb31ac8ce394e2137.md)

# Version 2.0.0 (08.05.2024)

**GitLab**: [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/tree/2.0.0](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/tree/2.0.0?ref_type=tags)

**Implemented**:

- [“CRM (Syluis) → Shopify” Live Sync](%E2%80%9CCRM%20(Syluis)%20%E2%86%92%20Shopify%E2%80%9D%20Live%20Sync%201d5686eae52c80b7bd3ef39f454e9f01.md)

## **Details (click to see)**:

### **🛒 Order & Refund Syncing with Shopify**

- Implemented automatic live exporting of new orders to Shopify
- Implemented automatic live exporting of new refunds to Shopify
- Implemented saving of shopifyIds for exported order’s payments and order items (needed for refund exports)
- Adjusted the “pull orders data” command to include necessary data for historical orders (enabling refund exports)
- Implemented management of Shopify info on the Order detail page:
    - Shows export status for product, customer, order, payment method, subscription
    - Provides a “Export full order data to Shopify” retry/export button

### **🧾 Product & Variant Export Improvements**

- Now products with their variants are correctly exported with additional options (e.g. flavors) to Shopify

### **🌐 Multichannel Support & Configuration**

- Multichannel compatibility implemented:
    - Refactored Shopify-related configuration:
        - Each “Shopify App Config” entry must now belong to a particular channel
        - Appstle API key, Shopify shop domain & ID are now set per channel in the admin UI
    - Console commands now require a mandatory channel-code argument:
        - AppstleExportSubscriptionContractsCommand
        - BulkExportOrderToShopifyCommand
        - BulkUpdateOrderOnShopifyCommand
        - BulkExportCustomerToShopifyCommand
        - BulkExportCustomerToShopifyViaRestCommand
        - BulkUpdateCustomerOnShopifyViaRestCommand
        - BulkExportStripePaymentDataCommand
        - BulkPullOrderDataFromShopifyCommand
        - BulkImportCustomersPaymentDataCommand
        - DetectOrdersThatNeedToBeReExportedToShopifyCommand
        - ExportProductToShopifyCommand
        - PreExportOrderValidationCommand

### **🔔 Slack Notifications**

- Created a Slack channel for Shopify sync issue alerts
- Added SLACK_WEBHOOK_URL_SHOPIFY_SYNC_NOTIFICATIONS env variable for configuring alerts

### **🧩 Admin UI Improvements**

- Added toggleable “Synced with Shopify?” column and filter to:
    - Orders grid
    - Products grid
    - Customers grid
- Added a history log entry for each order exported to Shopify

### **📬 Email Filtering for Live Sync**

- Disabled non-critical Sylius emails when Live Sync is enabled for the channel:
    - new_shop_user_created
    - units_refunded
    - order_confirmation
    - order_confirmation_resent
    - user_registration
    - verification_token
    - shipment_confirmation
    - Stripe subscription-specific:
        - subscription_confirmation
        - order_subscription_reminder
- Justification and further details documented [here](%E2%80%9CCRM%20(Syluis)%20%E2%86%92%20Shopify%E2%80%9D%20Live%20Sync%201d5686eae52c80b7bd3ef39f454e9f01/Emails%20Disabled%20in%20Sylius%201ed686eae52c8038bc4bd87e2fc41470.md)

### **🧹 Code Cleanup & Refactoring**

- Fixed PHP code style issues (phpcs)
- Fixed PHP static analysis errors (phpstan)
- Refactored Shopify-related fields in CRM-Core entities:
    - Removed unused fields
    - Entities only contain used fields instead of a shared trait with all fields

### **🧪 Removed Testing Artifacts**

- Removed UI-based batch exporting of customers, products, and orders
- Removed deletion commands for exported Shopify data (dangerous functionality)