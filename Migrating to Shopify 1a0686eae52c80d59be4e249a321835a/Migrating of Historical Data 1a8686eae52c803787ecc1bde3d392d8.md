# Migrating of Historical Data

# Which Data We Export to Shopify

## Products

We transform product data during migration to Shopify.

### Notes About Channel

- We have to specify a channel code in the products exporting command (from there - the app will know which Shopify store export products to)
- Product prices will be taken for this specified channel also (if they are set)
- Bundle code (warehouse SKU) will be taken for this specified channel also (see the “Bundle Products” below for details)

### Grouped and Bundle Products

All existing products fall into two categories: **grouped products** and **bundle products**.

We sort and migrate them as follows:

- **Bundle Products**
    
    If a product has the **“promotion_warehouse_sku”** code filled in any of its **ChannelPricing** or **ChannelPricingItems**, we treat it as a **“bundle”** product and export it separately as a standalone bundle product.
    
- **Grouped Products**
    
    If the product is **NOT** a bundle product (based on the criteria above), we merge it with all related products that have the same **Product → MintSoftSku**.
    

### Variants Merging

Since, on the Sylius/CRM side, “Subscribe and Save” and “One Time Purchase” are values of the “subscription” option, while on the Shopify side, they fall under the “purchase option” category rather than a “product option,” and there are no distinct product variants for “One Time Purchase” and “Subscribe and Save” cases, we remove the “subscription” option from all product variants on the Sylius/CRM side and merge such pairs of variants into one when exporting to Shopify.

### CRM → Shopify Data Mapping for Products

| CRM field | Shopify Field |
| --- | --- |
| description | descriptionHtml (if it’s empty yet, not overwritten otherwise) |
| slug | handle (if it’s empty yet) |
| enabled ? ‘ACTIVE’ : ‘ARCHIVED’ | status (if it’s empty yet) |
| name | title (if it’s empty yet) |
| images | files |
| sku | Metafields[malaberg.sku] |
| mintSoftSku | Metafields[malaberg.mintsoft_sku] |
| mintSoftName | Metafields[malaberg.mintsoft_name] |
| productCostSku | Metafields[malaberg.product_cost_sku] |
| options (with removed “subscription” option) | productOptions (if it’s empty yet) |
| variants (but after removed “subscription” option and merging “subscribe and save” and “one time purchase” variants into one variant. If there are another options in variant - they remain untouched)  | variants |

## Customers

We export all existing customers to Shopify.

### Notable details:

- **Handling of Invalid Phone Numbers**
    
    Shopify has significantly stricter validation rules than our CRM for customer and address phone numbers.
    
    In particular, while we check phone numbers for compliance with the E.164 standard, Shopify also verifies whether regional codes within countries are correct and exist.
    
    As a result, Shopify may reject customer data due to an invalid phone number, even if it complies with the E.164 format.
    
    In such cases, we retry submitting the customer’s data but without the invalid phone number.
    
    Since we always export the original customer’s phone number in the malaberg.phone metafield, we can identify affected customers by filtering for those who have this metafield filled but have an empty phone number in their Shopify profile.
    
- **Handling of the “Email is Already Taken” Error**
    
    When Shopify returns this error, the app retrieves the existing customer’s ID from Shopify and simply assigns this ID to the corresponding customer record in our database, without re-exporting their data.
    

### CRM → Shopify Data Mapping for Customers

| CRM field | Shopify Field |
| --- | --- |
| email | email
Metafields[malaberg.crm_email] |
|  | emailMarketingConsent = SUBSCRIBED |
| firstName | firstName |
| lastName | lastName |
| id | Metafields[malaberg.crm_id] |
| convertKitSubscriberId | Metafields[malaberg.convertkit_subscriber_id] |
| ermId | Metafields[malaberg.erm_id] |
| birthday | Metafields[malaberg.birthday] |
| createdAt | Metafields[malaberg.created_at] |
| updatedAt | Metafields[malaberg.updated_at] |
| gender | Metafields[malaberg.gender] |
| ipAddress | Metafields[malaberg.ip_address] |
| paymentKlarnaCustomerToken | Metafields[malaberg.payment_klarna_customer_token] |
| paymentReachCardLast4 | Metafields[malaberg.payment_reach_card_last_4] |
| paymentReachContractId | Metafields[malaberg.payment_reach_contract_id] |
| paymentReachDeviceFingerprint | Metafields[malaberg.payment_reach_device_fingerprint] |
| paymentReachPaymentMethod | Metafields[malaberg.payment_reach_payment_method] |
| paymentReachStashId | Metafields[malaberg.payment_reach_stash_id] |
| stripeId | Metafields[malaberg.stripe_id] |
| utmCampaign | Metafields[malaberg.utm_campaign] |
| utmContent | Metafields[malaberg.utm_content] |
| utmMedium | Metafields[malaberg.utm_medium] |
| utmSource | Metafields[malaberg.utm_source] |
| utmTerm | Metafields[malaberg.utm_term] |
| phoneNumber | phone
Metafields[malaberg.phone_number] |
|  | smsMarketingConsent = SUBSCRIBED (if phone is not empty) |
| addresses | addresses |

## Orders

When exporting order information, we also include the following related data:

- **Shipment information**
- **Payment information**
- **Refund payment information**
- **Paid taxes information**

### Finance Calculation for Order Export

Before exporting an order, we perform financial calculations to ensure it meets Shopify’s main validation criteria (i.e., it does not result in “Customer owes us money” or “We owe a refund to the customer” issues).

The order must satisfy the following equation:

**Subtotal (sum(price × quantity) for each order item) + Shipping Cost - Discount - Refunds amount = Payments amount - Refunds amount.**

If the order does not satisfy this equation, we iteratively apply the following **order-fixing policies** to correct it. If none of the applied policies resolve the issue, we still send the order to Shopify but include a note in the shopify_pre_export_validation_errors field documenting the detected validation issue.

If any of the applied policies successfully resolve the issue, we record the policy code in the shopify_pre_export_fix_policy field in the CRM database for that order.

### Orders Fixing Policies

- **revert_order_action_history**
    
    Since we manage subscription frequency, items, quantities, and prices in the database by modifying the first (original) subscription order in the CRM, while also needing to export the initial historical data of this order to Shopify (i.e., what the customer originally paid), we sometimes need to restore the initial order values from records in the sylius_order_action_history table in the database.
    
- **remove_unpaid_items**
    
    Sometimes, orders contain unpaid items—these are listed in the sylius_order_item table but do not have an assigned payment_id (i.e., they have not been paid for). As a result, Shopify treats these items as unpaid and displays a note that the customer owes us money.
    
    With this policy, we attempt to detect and remove such unpaid items from the order data before exporting it to Shopify.
    
- **restore_only_original_quantity_from_order_item_units**
    
    Sometimes, the specified quantity in the sylius_order_item table does not match the number of corresponding records in sylius_order_item_unit. In such cases, we attempt to restore the original quantity by counting the sylius_order_item_unit records and verifying whether the restored quantity satisfies the main order’s financial equation.
    
- **restore_original_quantity_and_price_from_order_item_units**
    
    This policy works the same way as **“restore_only_original_quantity_from_order_item_units”**, but in addition, we also retrieve the **“units_total”** from the corresponding sylius_order_item record and divide it by the restored quantity to determine the original order price.
    
    This approach is effective because the **“units_total”** value often remains unchanged from the initial order values, even when price or quantity changes occur later. As a result, we can accurately restore the initial order price from it.
    
- **restore_only_original_price_from_order_item_units_total**
    
    This policy is the same as **“restore_original_quantity_and_price_from_order_item_units”**, but instead of restoring the quantity, we use the **“units_total”** column value to restore the initial price while keeping the current quantity unchanged in the database.
    
- **loaded_adjusted_request_from_db**
    
    This is the most powerful method for fixing order export issues.
    
    The shopify_adjusted_request column in the sylius_order table contains a JSON-encoded request. If this column has a value, we decode the request from this value and send it to Shopify instead of generating a new request dynamically.
    
    This policy should be applied only when none of the previous fixing policies work, and the order has already been exported to Shopify with an incorrect status, such as **“We owe a refund to the customer”** or **“The customer owes us money.”**
    
    In such cases, we retrieve the original request sent to Shopify from the shopify_request column, modify the data as needed, and save the adjusted request in the shopify_adjusted_request column.
    
    If this column is not empty, the app:
    
    1.	Uses its value.
    
    2.	Logs **“loaded_adjusted_request_from_db”** in the shopify_pre_export_fix_policy column of the sylius_order table.
    
    3.	Skips all other data modifications.
    
    4.	Sends the decoded request to Shopify.
    

### CRM → Shopify Data Mapping for Orders

| Sylius/CRM data | Shopify data |
| --- | --- |
| billingAddress | billingAddress |
|  | buyerAcceptsMarketing = TRUE |
| checkoutCompletedAt | closedAt |
| currencyCode | currency |
| customer | customer |
| items | lineItems |
| shipments→first()→getState() | Metafields[malaberg.crm_shipment_original_state] |
| number | Metafields[malaberg.crm_order_number] |
| id | Metafields[malaberg.crm_order_id] |
| createdFromOrder→id | Metafields[malaberg.created_from_order_id] |
| createdFromOrder→shopifyId | Metafields[malaberg.created_from_order_shopify_id] |
| createdFromOrder→number | Metafields[malaberg.created_from_order_number] |
| klaviyoId | Metafields[malaberg.klaviyo_id] |
| state | Metafields[malaberg.crm_state] |
| shippingState | Metafields[malaberg.crm_shipping_state] |
| utmCampaign | Metafields[malaberg.utm_campaign] |
| utmContent | Metafields[malaberg.utm_content] |
| useCardId | Metafields[malaberg.crm_use_card_id] |
| utmId | Metafields[malaberg.utm_id] |
| utmMedium | Metafields[malaberg.utm_medium] |
| utmSource | Metafields[malaberg.utm_source] |
| utmTerm | Metafields[malaberg.utm_term] |
| Calculated from shippingState and paymentState | fulfillmentStatus |
| shipment, and also derived from the shippingState and paymentState values | fulfillment |
| adjustmentsTotalRecursively (if they are < 0) | discountCode |
| currencyCode | presentmentCurrency |
| checkoutCompletedAt | processedAt |
| shippingAddress | shippingAddress |
| number | sourceIdentifier |
|  | Tags[migrated] (for all migrated orders) |
|  | Tags[crm_id_XXX] where XXX is the order ID in CRM DB |
| payments (and the corresponding payment→refundPayments if any) | transactions |

## About Taxes

In general, **we send information about taxes in orders data, assuming the taxes payment is included in the entire sum that the customer has paid.**

At the moment we imply that orders payments contain only the VAT tax.

The VAT rates are specified in the [**$vatRates set of the TaxCalculatorService**](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/-/blob/main/src/Service/TaxCalculatorService.php?ref_type=heads#L10).

If there is no country in the set above - we imply that the payment from customers with shipping address in this country does not contain any VAT.

In details: There are following Tax-related attributes in data types which we export to Shopify:

### **Customer**

([CustomerInput object in Shopify docs](https://shopify.dev/docs/api/admin-graphql/latest/input-objects/CustomerInput))

- **taxExempt** (Whether the customer is exempt from paying taxes on their order)
- **taxExemptions** (The list of tax exemptions to apply to the customer.)

We do not send it at all to Shopify in the Customer data - se, they empty by default

- [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/e94f6d6a8ef68ddf082a0[…]src/Shopify/GraphQl/Mapper/InputObjects/CustomerInputMapper.php](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/e94f6d6a8ef68ddf082a0aa953626077ebb59dd3/src/Shopify/GraphQl/Mapper/InputObjects/CustomerInputMapper.php#L310)
- [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/27aa738691d66015e49da[…]b21db2/src/Shopify/GraphQl/Types/InputObjects/CustomerInput.php](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/27aa738691d66015e49da4d394d587f7f7b21db2/src/Shopify/GraphQl/Types/InputObjects/CustomerInput.php#L45)
- [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/27aa738691d66015e49da[…]b21db2/src/Shopify/GraphQl/Types/InputObjects/CustomerInput.php](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/27aa738691d66015e49da4d394d587f7f7b21db2/src/Shopify/GraphQl/Types/InputObjects/CustomerInput.php#L106)

### **Order** level

 ([OrderCreateOrderInput objec in Shopify docs](https://shopify.dev/docs/api/admin-graphql/latest/input-objects/OrderCreateOrderInput)) (for example we have a VAT rate = 20%)

- lineItems[]
    - quantity
    - priceSet
        - shopMoney
            - amount = **(price per unit)**
    - taxable = **true**
    - taxLines[] **(always empty)**
- **shippingLines**[]
    - priceSet
        - shopMoney
            - amount = **(shipping price)**
    - **taxLines**[]
        - channelLiable = **null**
        - priceSet
            - shopMoney
                - amount = **(shipping price / 120% * 20%) = tax**
        - rate = **0.2**
        - title = **‘VAT’**
- **taxesIncluded** = **true**
- **taxLines**[]
    - channelLiable = **null**
    - priceSet
        - shopMoney
            - amount = **(gross total (quantity * price per unit for all line items) / 120% * 20%) = tax**
    - rate = **0.2**
    - title = **‘VAT’**

### **Product Variant**

 ([ProductVariantSetInput object in Shopify docs](https://shopify.dev/docs/api/admin-graphql/latest/input-objects/ProductVariantSetInput))

- **taxable**
- **taxCode**

We keep them empty (do not send them to Shopify at all):

- [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/92efb7dd5b7989e82badd[…]c/Shopify/GraphQl/Types/InputObjects/ProductVariantSetInput.php](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/92efb7dd5b7989e82badd879d45750dbd27e23b2/src/Shopify/GraphQl/Types/InputObjects/ProductVariantSetInput.php#L45-47)
- [https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/7d074962f3b854b98d0ae[…]fy/GraphQl/Mapper/InputObjects/ProductVariantSetInputMapper.php](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/7d074962f3b854b98d0aeaf62b1fa41d1b935f77/src/Shopify/GraphQl/Mapper/InputObjects/ProductVariantSetInputMapper.php#L15)

### Appstle **Subscription Contract**

We have the "**taxes**" field set to "**true**":

- [In the "**SubscriptionLineConnection**" of the subscription contract - Appstle (it looks like by default) sets the "**taxable**" field's value to "**true**"](https://gitlab.com/malaberg1/sylius-shopify-sync-plugin/blob/2be17c09ec8af58e573a93e7dacc82093f5e9f3e/src/Resources/docs/examples/appstle_create_subscription_response.json#L61)
- While [we have no way to set it to false - they do not accept any related field in the Create Subscription Contract request](https://subscription-admin.appstle.com/swagger-ui/appstle-subscriptions-api.html#/subscription-contract-details-resource/createSubscriptionContractV2).

# How To Perform Migration

## Prepare the Shopify Store

1. **Fix partially_paid orders**
2. **Disable notifications** about the created orders:
    1. Login to Shopify Store
    2. Go to Settings → Notifications
    3. Disable all
        1. Customers notifications
        2. Staff notifications
3. **Disable below apps**
    1. Mintsoft
    2. Klaviyo
    3. Xero
4. **Migrate coupons**

## Prepare the Sylius Storefront app (CRM)

1. **Make dump** of DB - for verification
2. **Enable the “Shopify Integration Management” role** in Admin UI → RBAC → Administration Roles for the needed admin roles (e.g. “Configurator”).
    
    There should appear the “Shopify” section at the bottom of the sidebar menu.
    
3. **Prepare the DB**
    
    **If** it’s the **very first exporting** on the current DB instance is planned - then **no actions needed**.
    
    **Otherwise** (if there were testing exports from the current DB - e.g. to some “pre-prod” Shopify sites) - we need to clean up the DB from the Shopify data:
    ! Important! We never should perform such clean-up if we already have exported the data to real prod site, and the DB contains Shopify data from this site (otherwise we will not be able to update/delete the exported data, and any new exports may duplicate the previously exported records)
    
    - SQL:
        
        ```sql
        update
        	prod_dss.sylius_customer
        set
        	shopify_id = null,
          shopify_errors = null,
          shopify_app_client_id = null,
          shopify_request = null,
          shopify_update_needed = null,
          shopify_planned_request = null,
          shopify_pre_export_validation_errors = null,
          shopify_pre_export_validated = null,
          shopify_pre_export_fix_policy = null,
          shopify_adjusted_request = null
        where
            id > 0
        ;
            
        update
        	prod_dss.sylius_order
        set
        	shopify_id = null,
          shopify_errors = null,
          shopify_app_client_id = null,
          shopify_request = null,
          shopify_update_needed = null,
          shopify_planned_request = null,
          shopify_pre_export_validation_errors = null,
          shopify_pre_export_validated = null,
          shopify_pre_export_fix_policy = null,
          shopify_adjusted_request = null,
          shopify_pull_flag = null,
          shopify_shipping_cost = null,
          shopify_summarized_discount = null
        where
            id > 0
        ;
        
        update
        	prod_dss.sylius_product
        set
        	shopify_id = null,
          shopify_errors = null,
          shopify_app_client_id = null,
          shopify_request = null,
          shopify_update_needed = null,
          shopify_planned_request = null,
          shopify_pre_export_validation_errors = null,
          shopify_pre_export_validated = null,
          shopify_pre_export_fix_policy = null,
          shopify_adjusted_request = null,
          shopify_online_store_preview_url = null,
          shopify_online_store_url = null
        where
            id > 0
        ;
        
        update
        	prod_dss.sylius_product_variant
        set
        	shopify_id = null
        where
            id > 0
        ;
        ```
        

## Create, Configure and Install Shopify Exporting Apps

**Video explanation:**

[shopify_exporting_app.mp4](Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8/shopify_exporting_app.mp4)

**Text Version**

1. Go to Shopify → Apps Configuration section of the Sylius storefront admin UI
2. Press the “Add” button
3. Put a meaningful name for the app (to distinquish it from others - it’s convenient in some cases)
4. Go to Apps → Create App in the Shopify Partners dashboard
5. Press the “Create App” button
6. In the “Use Shopify Partners” block press the “Create app manually” button and put the app’s name in the appeared field (the same as at the step 3 above), press the “Create” button
7. From the appeared page, copy the app data from Shopify to Sylius app:
    1. Client ID → App client id
    2. Client secret → App client secret
8. In the app configuration form on Sylius admin UI page, also put:
    1. Shopify shop domain: the domain of the Shopify store (without any slashes and prefixes like “https://”
    2. Shopify App Install Success URL https://{sylius.storefront.domain}/shopify_sync/install/success/{shopify_app_client_id}
    3. Press the “Save changes” button
9. Go to the app’s “Configuration” page on Shopify Partners dashboard (of the just created app) and put
    1. App URL: https://{sylius.storefront.domain}/shopify_sync/install/{shopify_app_client_id}
    2. Allowed redirection URL(s): https://{sylius.storefront.domain}/shopify_sync/install/success/{shopify_app_client_id}
    3. “Embed app in Shopify admin” set to “False”
    4. Press the “Save and Release” button
10. Go back to the “Overview” page in Shopify Partners dashboard (for the current app) and:
    1. Presse the “Choose Distribution” button
    2. Select “Custom Distribution”
    3. Put the Shopify store domain (without any prefixes and trailing slashes) to the “domain” input
    4. Uncheck the “Allow this app to be installed on Plus stores”
    5. Press the “Create Link” button
11. Install application:
    1. Copy the installation link that just has been created at the step above to an address input of any web-browser, go to the link
    2. On the appeared “Apps installation” page press the “Install” button
    3. Once you see JSON with the “access_token” string - the installation is complete
12. Enable application (optional)
    1. Once the app is fully cofigured - you can enable it - and it will be used by exporters among other enabled exporting apps. Until that (while it’s not enabled) - it’s ignored by the exporters and can be configured further.
    2. On the “Apps Configuration” page on Sylius Admin UI toggle the “Enabled” checkbox for the just created app
    3. Save changes

The app is fully configured and will be used in all exporting jobs.

**Rinse and repeat** steps above to create **additional exporting apps** (which will enable us to scale exporting horizontally, by dispersing the exporting data between the enabled and configured exporting apps.

## Run Historical Migration

The historical migration **must** be performed in the following sequence: **products → customers → orders**.

It’s because orders must already know and reference to the shopify IDs of the previously exported customers (otherwise it may take additional time for orders exporting or even will lead to un-clear error messages from Shopify about particular orders).

### 1. Product Migration

1. **If products exist in Shopify store** - export from Shopify than first, wait for CSV to be emailed
2. **Export Products** Data
    
    Run the following console command in the Sylius Storefront server’s command line:
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:export_product_to_shopify CHANNEL_CODE -vvv
    ```
    
3. **Merge the duplicated products**
    
    **If** we have a case when we **already had configured product X on Shopify** before the product exporting has been performed - now the exporting command created its duplicate (by Product → MintSoftSku).
    
    Now we need to merge these duplicate products by:
    
    1. Get Shopify ID of the pre-existing product
    2. Put this ID to all products in the Sylius DB that have the ID of just created duplicate (replace the “just created product ID with the pre-existing product ID)
    3. Run the command second time: it will replace/set the correct variant IDs for the merged products in Sylius DB.
    4. Remove the duplicate (recently created during the exporting) product from Shopify
4. **Upload broken images**
5. **Run the exporting command second (third) time**
    
    This run will put correct prices to Shopify Products (we are not able to do it in the first run because we didn’t know IDs of “hidden” variants that Shopify creates for “single variant” products.
    
6. **Set SKU for bundles to map to Mintsoft bundles**
7. **Update Products**
If products data have been changed on the Sylius/CRM side - it’s safe to re-run the same exporting commands again.
    
    But the app will not send/update the following information on Shopify, if it’s already filled for Shopify’s products (as we assume that it’s the Shopify side is the source of truth for the product data after the initial exporting):
    
    - descriptionHtml
    - media (images)
    - a handle (slug)
    - metafields will be merged. The existing Shopify’s third-party metafields will be remained as is, the “malaberg”-namespaced metafields will be updated with the actual values from CRM
    - product options
    - variants
    - status
    - title

### 2. Customers Migration

1. Run a **single-customer testing migration**
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:export_customer_to_shopify_via_rest CHANNEL_CODE {any_enabled_exporting_app_client_id} --customer_id={customer_id} -vvv
    ```
    
2. Run **testing bulk migration** via command line
    
    This will export 2 customers per each enabled exporting app and will show the results.
    
    ```bash
    bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_customer_to_shopify_via_rest CHANNEL_CODE -b 2 -vvv
    ```
    
3. Put the **exporting on cron** (it will significantly load server) during the exporting
    
    The command below is “locking” - means it’s safe to run it every minute - it won’t run if the previous run is not finished yet.
    It will assign 50 next customers to each exporting app and run (fork) another exporting command for each app in parallel (50 - it’s because exactly this amount 1 app is able to export per minute accordingly to tests).
    
    ```bash
    * * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_customer_to_shopify_via_rest CHANNEL_CODE -b 50
    ```
    
    where *{project_home_dir}* is the project directory where the “bin” folder is situated, e.g.: `/home/<USER>/domains/doctorsisterskincare.com/public`
    
4. **Confirm in database no errors**
    
    ```sql
    SELECT * FROM sylius_customer where shopify_errors is not null
    ```
    

### 3. Orders Migration

- **Export Orders**
    1. Run testing migration for 1 order and check the results
        
        ```bash
        bin/console malaberg_sylius_shopify_sync_plugin:export_order_to_shopify CHANNEL_CODE {exporting_app_client_id} --order_id={order id} -vvv
        ```
        
    2. Run testing bulk migration via command line
        
        It will export 2 orders via each enabled exporting app in parallel, and will show the results and log on the screen.
        
        ```bash
        bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_order_to_shopify CHANNEL_CODE -b 2 -vvv
        ```
        
    3. Put the exporting on cron (again, it will significantly load server)
        
        It will export 50 orders per minute per each enabled exporting app.
        
        ```bash
        * * * * * cd {project_home_dir} && /usr/local/bin/php bin/console malaberg_sylius_shopify_sync_plugin:bulk_export_order_to_shopify CHANNEL_CODE -b 50
        ```
        
- **Check Orders for Exporting Issues**
    1. Verify there are no errors/complains from Shopify:
        
        ```sql
        select * from sylius_order where shopify_errors is not null;
        ```
        
    2. Verify that there is no “validation errors” for orders, meaning in the order neither
        1. we owe a refund to the customer
        2. the customer owe a payment to us
        3. the order is not in the “fulfilled” state
        
        ```sql
        select * from sylius_order where shopify_pre_export_validation_errors is not null;
        ```
        
        If there are any “validation errors” - see below how to find a cause and how to fix them.
        
- **Fix Orders**
    - ***Errors From Shopify***
        
        (sylius_order.shopify_erorrs is not null)
        
        Most likely it’s just some particular connection issues or similar errors, hence it will be enough to clear the errors and give the exporting another try:
        
        ```sql
        update sylius_order set shopify_errors = null where shopify_errors is not null and id > 0;
        ```
        
    - ***VIOLATION_FULFILLMENT_STATUS***
        
        (sylius_order.shopify_pre_export_validation_errors like ‘%VIOLATION_FULFILLMENT_STATUS%’)
        
        It always means that the order is unexpectedly in the NOT FULFILLED state.
        
        The only valid possible case when the order can be in NOT FULFILLED state without this error risen - it’s if it’s a new order, in the “ready” shipping_state.
        
        In any other cases, the NOT FULFILLED state is not valid and causes this error.
        
        The correct way to fix may vary and should be investigated in each case independently, but the most often way - is the changing of the order’s shipping_state to the correct one, e.g.:
        
        ```sql
        update sylius_order set shipping_state = 'cancelled' where id = 123;
        ```
        
    - ***WARNING_NOT_FULFILLED***
        
        It’s not a violation - it’s just a flag that the order is not in the FULFILLED state - including cases when it’s totally correct. The aim of this code - is to mark orders that are “not fulfilled” in Shopify and just to check how they are shown on the Shopify side.
        
    - ***VIOLATION_REFUND_OWED***
        
        It means that the customer has paid us more that their was obligated to (as it’s calculated by Shopify) and we owe a refund to them.
        
        Usually it means that there is some redundant discount in the Sylius/CRM DB for this order.
        
        **Or** there is some payment that in reality is refunded or cancelled, but in the DB it’s has the “authorized” state or even “paid”.
        
        **How to verify and fix correctness of discounts:**
        
        1. Check and adjust the “adjustments_total” value on each possible level for this order (for instance it has id = 123):
            1. sylius_order
                
                ```sql
                select * from sylius_order where id = 123;
                ```
                
            2. sylius_order_item
                
                ```sql
                select * from sylius_order_item where order_id = 123;
                ```
                
            3. sylius_order_item_unit
                
                ```sql
                SELECT oiu.* FROM sylius_order_item_unit oiu
                	left join sylius_order_item oi on oiu.order_item_id = oi.id
                    left join sylius_order o on o.id = oi.order_id and o.id = 123
                    where o.id is not null
                    order by oiu.order_item_id, oiu.id
                    ;
                ```
                
        2. Check and adjust the original adjustments amount in the “sylius_adjustment” table:
            
            ```sql
            SELECT * FROM sylius_adjustment
            	where order_id = 123
                or order_item_id in (
            			SELECT id FROM sylius_order_item where order_id = 123
                )
                or order_item_unit_id in (
            			SELECT oiu.id FROM sylius_order_item_unit oiu
            			left join sylius_order_item oi on oiu.order_item_id = oi.id
            			left join sylius_order o on o.id = oi.order_id and o.id = 123
            			where o.id is not null
                )
            ;
            ```
            
            ```sql
            UPDATE prod_dss.sylius_adjustment
            SET amount = 0
            WHERE id IN (1234567);
            ```
            
        
        **How to verify and fix correctness of payments:**
        
        Most often there is (are) redundant payments in the “authorized” or “completed” state (which exceeding the needed amount to pay), which do not have the corresponding refund payments (the app ignore payments in other states (cancelled, new, failed, cart), and the payments in “refunded” and “partially_refunded” state are never a subject for this issue - as any issues with them rather fall into the next category “partially paid” (see below).
        
        Hence, usually we need to pay attention to the order’s “authorized” and “completed” redundant payments:
        
        ```sql
        SELECT * from prod_dss.sylius_payment where order_id = 123;
        ```
        
        Example fix:
        
        ```sql
        UPDATE prod_dss.sylius_payment SET STATE = 'cancelled' WHERE order_id = 123;
        ```
        
    - ***VIOLATION_PARTIALLY_PAID***
        
        It means that the customer owes us a portion or a full amount of money they have to pay for an entire order (accordingly to Shopify’s calculations).
        
        The most often cases are:
        
        1. Order’s **payments in wrong status** (they have been performed in reality, but have others from “completed” and “authorized” state).
            
            Check:
            
            ```sql
            SELECT * from prod_dss.sylius_payment where order_id = 123;
            ```
            
            Example fix:
            
            ```sql
            UPDATE prod_dss.sylius_payment SET state = 'completed' WHERE id = 1234567;
            ```
            
        2. **Order has redundant refunds** (for instance refunds that have not been performed really, but they have the “Completed” state).
            
            Check:
            
            ```sql
            SELECT * FROM prod_dss_mariadb.sylius_refund_payment
            WHERE payment_id IN (
            		SELECT id FROM prod_dss_mariadb.sylius_payment WHERE order_id = 123
            );
            ```
            
            Example fix:
            
            ```sql
            UPDATE prod_dss_mariadb.sylius_refund_payment
            SET state = 'Cancelled' WHERE id = 1234567;
            ```
            
        3. **Order misses some discount** (e.g. free shipping, or another type)
            
            See the “*VIOLATION_REFUND_OWED →* How to verify and fix correctness of discounts” section above for details.
            
        4. **There is a redundant order item** (or several) which have not been sent with the order in reality, but the order does contain them (and they even have the payment_id set in the DB).
            
            Check:
            
            ```sql
            SELECT FROM prod_dss.sylius_order_item WHERE order_id = 123;
            ```
            
            Example fix:
            
            ```sql
            UPDATE prod_dss.sylius_order_item SET order_id = 1 WHERE id = 1234567;
            ```
            
        
- **Update Fixed Orders**
    
    There are two options:
    
    1. **If there are a lot of such orders**, you can run bulk update:
        1. Set for such orders in the DB:
            1. shopify_errors = null
            2. shopity_update_needed = 1
        2. Re-run the "bulk orders update" command manually in console
            
            ```bash
            bin/console malaberg_sylius_shopify_sync_plugin:bulk_update_order_on_shopify CHANNEL_CODE -b 10 -vvv
            ```
            
    2. **If it's a single order** (or several that you would like to update one-by-one), run per each order (without any preliminary clearance from Shopify Errors, etc.)
        
        ```
        bin/console malaberg_sylius_shopify_sync_plugin:update_order_on_shopify {any_exporting_app_client_id} --order_id=XXXX -vvv
        ```
        

# How To Verify Migration Correctness

There are four ways to retrieve statistics from Shopify about the exported data, ranked from least to most powerful:

1. **Directly in the Shopify Admin UI** (e.g., total number of customers).
2. **Reports in the “Analytics → Reports” section** of the Shopify Admin UI.
3. **ShopifyQL queries** in the “Analytics → Reports” section to retrieve calculated **sales** statistics.
4. **Extracted from exported data** (e.g., customers, orders) and analyzed externally.

For each statistic listed below, the most suitable method for obtaining the required data from Shopify is specified, though alternative methods from the list above may also be available.

## Products

**By manual comparison.**

Migrated products should comply with the transformation policies specified above in the **“Which Data We Export to Shopify → Products”** section.

## Customers

### Total Number of Customers

- **Shopify side**
    
    Go to the Customers page in Shopify Admin UI.
    
    There is a number of total customers at the top left corner of the customers grid:
    
    ![Screenshot 2025-03-06 at 09.08.08.png](Migrating%20of%20Historical%20Data%201a8686eae52c803787ecc1bde3d392d8/Screenshot_2025-03-06_at_09.08.08.png)
    
- **Sylius/CRM side**
    
    ```bash
    SELECT count(*) FROM prod_dss.sylius_customer;
    ```
    

### Number of Customers With Orders

- **Shopify side**
    
    Go to “Analytics → Reports → New Exploration”.
    
    Put and run the following ShopifyQL query:
    
    ```bash
    FROM sales
    SHOW customers;
    ```
    
- **Sylius/CRM side**
    
    ```sql
    SELECT count(DISTINCT(c.id)) FROM sylius_customer c
    INNER JOIN sylius_order o ON c.id = o.customer_id
    INNER join sylius_order_item oi on o.id = oi.order_id
    WHERE oi.quantity > 0
    AND (
    	(
    		o.`state` in ('new', 'fulfilled')
    		AND o.payment_state in ('paid', 'partially_refunded', 'refunded')
    	) OR (
    		o.`state` = 'cancelled'
    		AND o.payment_state in ('partially_refunded', 'refunded')
    	)
    );
    ```
    

## Orders

Due to [delays of up to 72 hours between order creation or modification in Shopify and their appearance in Shopify’s calculated “sales” statistics table](https://community.shopify.com/c/shopify-discussions/report-inconsistencies-for-discount-code-sales/td-p/2110306), the **most reliable method** to obtain the correct orders statistics on Shopify side **is to export orders from Shopify** and perform statistical queries on the exported dataset.

### Export Back and Prepare Orders Data from Shopify

1. Backup the Sylius/CRM DB!
2. Go to the Orders page in Shopify Admin UI
3. Press the “Export” button
4. Choose “All orders”
5. Press the “Export Orders” button
6. Wait for an email from Shopify with links to download zip archives with CSV files with the exported orders data
7. Download and unzip the CSV files
8. Import data from the CSV files into Sylius/CRM database (to a dedicated new table, let’s say “shopify_exported_orders”)
9. Apply the following SQL queries on the exported data to make it better prepared for stats calculations:
    - SQL:
        
        ```sql
        # --- crm_id ---
        ALTER TABLE shopify_exported_orders ADD COLUMN `crm_id` INT NULL AFTER `Name`;
        UPDATE shopify_exported_orders
        SET
        	crm_id = SUBSTRING_INDEX(SUBSTRING_INDEX(Tags, 'crm_id_', -1), ',', 1)
        WHERE
        	Tags LIKE '%crm_id_%';
        
        # --- int_subtotal ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_subtotal INT NULL AFTER crm_id;
        UPDATE shopify_exported_orders
        SET
        	int_subtotal = CAST(
        		REPLACE
        			(`Subtotal`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Subtotal` IS NOT NULL
        	AND `Subtotal` != '';
        
        # --- int_shipping ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_shipping INT NULL AFTER int_subtotal;
        UPDATE shopify_exported_orders
        SET
        	int_shipping = CAST(
        		REPLACE
        			(`Shipping`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Shipping` IS NOT NULL
        	AND `Shipping` != '';
        
        # --- int_taxes ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_taxes INT NULL AFTER int_shipping;
        UPDATE shopify_exported_orders
        SET
        	int_taxes = CAST(
        		REPLACE
        			(`Taxes`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Taxes` IS NOT NULL
        	AND `Taxes` != '';
        
        # --- int_total ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_total INT NULL AFTER int_taxes;
        UPDATE shopify_exported_orders
        SET
        	int_total = CAST(
        		REPLACE
        			(`Total`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Total` IS NOT NULL
        	AND `Total` != '';
        
        # --- int_discount_amount ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_discount_amount INT NULL AFTER int_total;
        UPDATE shopify_exported_orders
        SET
        	int_discount_amount = CAST(
        		REPLACE
        			(`Discount Amount`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Discount Amount` IS NOT NULL
        	AND `Discount Amount` != '';
        
        # --- int_refunded_amount ---
        ALTER TABLE shopify_exported_orders ADD COLUMN int_refunded_amount INT NULL AFTER int_discount_amount;
        UPDATE shopify_exported_orders
        SET
        	int_refunded_amount = CAST(
        		REPLACE
        			(`Refunded Amount`, '.', '') AS UNSIGNED
        	)
        WHERE
        	`Refunded Amount` IS NOT NULL
        	AND `Refunded Amount` != '';
        	
        # --- Indexes ---
        ALTER TABLE `shopify_exported_orders`
        ADD INDEX `sheo_crm_id` (`crm_id`) USING BTREE,
        ADD INDEX `sheo_name` (`Name` (255)) USING BTREE;
        ```
        

### Total Number of Orders

- **Shopify side**
    
    ```sql
    SELECT count(DISTINCT crm_id) from shopify_exported_orders;
    ```
    
- **Sylius/CRM side**
    
    ```sql
    SELECT
    	count(DISTINCT (o.id))
    FROM
    	sylius_order o
    	INNER JOIN sylius_order_item oi ON o.id = oi.order_id
    WHERE
    	oi.quantity > 0
    	AND (
    		(
    			o.state IN ('new', 'fulfilled')
    			AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
    		)
    		OR (
    			o.state = 'cancelled'
    			AND o.payment_state IN ('partially_refunded', 'refunded')
    		)
    	);
    ```
    
- Discrepancy In CRM DB: **Exported, but NOT Eligible Orders:**
    
    ```sql
    SELECT
    	*
    FROM
    	sylius_order o1
    	LEFT JOIN (
    		SELECT DISTINCT
    			(o.id)
    		FROM
    			sylius_order o
    			INNER JOIN sylius_order_item oi ON o.id = oi.order_id
    		WHERE
    			oi.quantity > 0
    			AND (
    				(
    					o.state IN ('new', 'fulfilled')
    					AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
    				)
    				OR (
    					o.state = 'cancelled'
    					AND o.payment_state IN ('partially_refunded', 'refunded')
    				)
    			)
    	) o2 ON o1.id = o2.id
    WHERE
    	o1.shopify_id IS NOT NULL
    	AND o2.id IS NULL;
    ```
    
- Discrepancy In CRM DB: **Eligible, but NOT Exported Orders:**
    
    ```sql
    SELECT
    	o.*
    FROM
    	sylius_order o
    	INNER JOIN sylius_order_item oi ON o.id = oi.order_id
    WHERE
    	oi.quantity > 0
    	AND (
    		(
    			o.state IN ('new', 'fulfilled')
    			AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
    		)
    		OR (
    			o.state = 'cancelled'
    			AND o.payment_state IN ('partially_refunded', 'refunded')
    		)
    	)
    	AND shopify_id IS NULL;
    ```
    

### Gross Total

- **Shopify side**
    
    Shopify calculates a value of the “Total” column in the exported orders as:
    
    `Subtotal (sum(quantity * price of each line items)) + Shipping Cost - Discounts`
    
    To retrieve the Gross Total (a sum of all orders totals), run the following query:
    
    ```sql
    select FORMAT(sum(int_total) / 100, 2) AS gross_total from shopify_exported_orders;
    ```
    
- **Sylius/CRM side**
    
    ```sql
    SELECT
    	FORMAT(sum(o.total) / 100, 2)
    FROM
    	sylius_order o
    	INNER JOIN (
    		SELECT DISTINCT
    			(o3.id)
    		FROM
    			sylius_order o3
    			LEFT JOIN sylius_order_item oi ON o3.id = oi.order_id
    		WHERE
    			oi.quantity > 0
    			AND (
    				(
    					o3.state IN ('new', 'fulfilled')
    					AND o3.payment_state IN ('paid', 'partially_refunded', 'refunded')
    				)
    				OR (
    					o3.state = 'cancelled'
    					AND o3.payment_state IN ('partially_refunded', 'refunded')
    				)
    			)
    	) o2 ON o.id = o2.id;
    ```
    
- **Discrepancies in Gross Total between Shopify and CRM data**
    - Orders that have different “total” in CRM and Shopify:
        
        ```sql
        select
        	o.id, o.items_total, o.adjustments_total, o.total, o.shopify_id,
            m.int_subtotal, m.int_shipping, m.int_total, m.int_discount_amount, m.int_refunded_amount
            FROM sylius_order o
            INNER JOIN shopify_exported_orders m ON o.id = m.crm_id
            WHERE o.total != m.int_total;
        ```
        
    - Orders that are eligible for exporting to Shopify from CRM, but are absent in Shopify:
        
        ```sql
        SELECT
        	*
        FROM
        	sylius_order
        WHERE
        	id IN (
        		SELECT
        			o2.id
        		FROM
        			(
        				SELECT
        					o.id
        				FROM
        					sylius_order o
        					LEFT JOIN sylius_order_item oi ON o.id = oi.order_id
        				WHERE
        					oi.quantity > 0
        					AND (
        						(
        							o.state IN ('new', 'fulfilled')
        							AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
        						)
        						OR (
        							o.state = 'cancelled'
        							AND o.payment_state IN ('partially_refunded', 'refunded')
        						)
        					)
        			) o2
        			LEFT JOIN shopify_exported_orders m ON o2.id = m.crm_id
        		WHERE
        			m.`Name` IS NULL
        	);
        ```
        
    - Orders that are presented in the exported Shopify Data, but absent in a set of eligible for exporting CRM orders:
        
        ```sql
        SELECT
        	*
        FROM
        	shopify_exported_orders
        WHERE
        	crm_id IN (
        		SELECT
        			m.crm_id
        		FROM
        			shopify_exported_orders m
        			LEFT JOIN (
        				SELECT
        					o.id
        				FROM
        					sylius_order o
        					LEFT JOIN sylius_order_item oi ON o.id = oi.order_id
        				WHERE
        					oi.quantity > 0
        					AND (
        						(
        							o.state IN ('new', 'fulfilled')
        							AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
        						)
        						OR (
        							o.state = 'cancelled'
        							AND o.payment_state IN ('partially_refunded', 'refunded')
        						)
        					)
        			) o2 ON o2.id = m.crm_id
        		WHERE
        			o2.id IS NULL
        	);
        ```
        

### Refunds Total

- **Shopify side**
    
    ```sql
    SELECT FORMAT(SUM(int_refunded_amount) / 100, 2) AS refunds_total_shopify FROM shopify_exported_orders;
    ```
    
- **Sylius/CRM side**
    
    ```sql
    SELECT
    	FORMAT(sum(gt.total_refunded_amount) / 100, 2) AS refunds_total_crm
    FROM
    	(
    		SELECT
    			SUM(srp.amount) AS total_refunded_amount
    		FROM
    			sylius_refund_payment srp
    			JOIN sylius_payment sp ON srp.payment_id = sp.id
    			JOIN sylius_order o ON sp.order_id = o.id
    		WHERE
    			srp.state = "Completed"
    			AND o.id IN (
    				SELECT DISTINCT
    					(o.id)
    				FROM
    					sylius_order o
    					INNER JOIN sylius_order_item oi ON o.id = oi.order_id
    				WHERE
    					oi.quantity > 0
    					AND (
    						(
    							o.state IN ('new', 'fulfilled')
    							AND o.payment_state IN ('paid', 'partially_refunded', 'refunded')
    						)
    						OR (
    							o.state = 'cancelled'
    							AND o.payment_state IN ('partially_refunded', 'refunded')
    						)
    					)
    			)
    		GROUP BY
    			sp.order_id
    	) gt;
    ```
    
- **Discrepancies in Refunds between Shopify and CRM data**
    
    ```sql
    SELECT
    	o.id AS crm_order_id,
    	FORMAT(IFNULL(gt.total_refunded_amount, 0) / 100, 2) AS refunds_total_crm,
    	FORMAT(IFNULL(seo.int_refunded_amount, 0) / 100, 2) AS refunds_total_shopify
    FROM
    	sylius_order o
    	LEFT JOIN (
    		SELECT
    			sp.order_id,
    			SUM(srp.amount) AS total_refunded_amount
    		FROM
    			sylius_refund_payment srp
    			JOIN sylius_payment sp ON srp.payment_id = sp.id
    		WHERE
    			srp.state = "Completed"
    		GROUP BY
    			sp.order_id
    	) gt ON o.id = gt.order_id
    	LEFT JOIN shopify_exported_orders seo ON o.id = seo.crm_id
    WHERE
    	IFNULL(gt.total_refunded_amount, 0) <> IFNULL(seo.int_refunded_amount, 0);
    ```
    

# Troubleshooting

Useful SQL Queries in Sylius DB

Get all order’s adjustments

```sql
SELECT * FROM sylius_adjustment
	where order_id = **XXXXXXXX**
    or order_item_id in (
			SELECT id FROM sylius_order_item where order_id = **XXXXXXXX**
    )
    or order_item_unit_id in (
			SELECT oiu.id FROM sylius_order_item_unit oiu
			left join sylius_order_item oi on oiu.order_item_id = oi.id
			left join sylius_order o on o.id = oi.order_id and o.id = **XXXXXXXX**
			where o.id is not null
    )
;
# Put the order ID instead of **XXXXXXXX**
```

---

---

The notes below are already reflected in the documentation above and can be removed. However, they should be retained temporarily during the YPN transfer to save Karolis time from having to review the documentation in detail during this period.

---

Total

```sql
SELECT
    distinct(o.id), o.total
FROM
    sylius_order o
    INNER JOIN sylius_order_item oi
        ON o.id = oi.order_id
WHERE
    oi.quantity > 0
    AND (
        (o.state IN ('new', 'fulfilled')
         AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
        OR (o.state = 'cancelled'
            AND o.payment_state IN ('partially_refunded', 'refunded'))
    )
```

here are two options:

- If there are a lot of such orders, you can run bulk update:
    1. Set for such orders in the DB:
        1. shopify_errors = null
        2. shopity_update_needed = 1
    2. Re-run the "bulk orders update" command

```
bin/console malaberg_sylius_shopify_sync_plugin:bulk_update_order_on_shopify -b 10 -vvv

```

manually in console

- If it's a single order (or several that you would like to update one-by-one), run per each order (without any preliminary clearance from Shopify Errors, etc.)

```
bin/console malaberg_sylius_shopify_sync_plugin:update_order_on_shopify {any_exporting_app_client_id} --order_id=XXXX -vvv
```

In both cases orders automatically will be deleted in Shopify and re-created there.

Refunds

```sql
SELECT 
    sp.order_id AS order_id,
    SUM(srp.amount) AS total_refunded_amount
FROM 
    sylius_refund_payment srp
JOIN 
    sylius_payment sp 
    ON srp.payment_id = sp.id
JOIN 
    sylius_order o 
    ON sp.order_id = o.id
WHERE 
    srp.state = "Completed"
    AND o.id IN (
        SELECT DISTINCT(o.id)
        FROM sylius_order o
        INNER JOIN sylius_order_item oi
            ON o.id = oi.order_id
        WHERE 
            oi.quantity > 0
            AND (
                (o.state IN ('new', 'fulfilled')
                 AND o.payment_state IN ('paid', 'partially_refunded', 'refunded'))
                OR (o.state = 'cancelled'
                    AND o.payment_state IN ('partially_refunded', 'refunded'))
            )
    )
GROUP BY 
    sp.order_id;
```

Order of validation:

1. No duplicate orders
2. Check no orders missing
3. Check totals
4. Check refunds
5. Check no outstanding payment in Shopify data